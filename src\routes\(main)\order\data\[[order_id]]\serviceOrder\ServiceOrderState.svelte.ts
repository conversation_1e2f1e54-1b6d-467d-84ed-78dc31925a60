import type { <PERSON><PERSON>, <PERSON>ir, <PERSON>et, Sparepart } from '$lib/schema/general';
import type { ServiceType } from '$lib/schema/misc';
import { getContext, setContext } from 'svelte';

export interface CustomService {
	nama: string;
	harga: number;
}

export interface ServiceOrder {
	jenis_service: ServiceType | 'custom' | null;
	qty: number[];
	montir: Montir | null;
	service: Paket | Jasa[] | Sparepart[] | CustomService | null;
}

export interface ServiceOrderState {
	orders: ServiceOrder[];

	subtotal: number;
	diskon: number;
	ppn: number;
	total: number;

	addOrder(): void;
	removeOrder(index: number): void;
}

export default class ServiceOrderStateClass implements ServiceOrderState {
	orders = $state<ServiceOrder[]>([]);

	subtotal = $derived(
		this.orders.reduce((acc, curr) => {
			let subtotalPerService = 0;
			if (curr.jenis_service === 'paket') {
				subtotalPerService = (curr.service as Paket).harga;
			} else if (curr.jenis_service === 'custom') {
				subtotalPerService = (curr.service as CustomService).harga;
			} else if (curr.jenis_service === 'jasa') {
				subtotalPerService = (curr.service as Jasa[]).reduce(
					(accService, currService, indexService) =>
						accService + currService.harga * curr.qty[indexService],
					0
				);
			} else if (curr.jenis_service === 'sparepart') {
				subtotalPerService = (curr.service as Sparepart[]).reduce(
					(accService, currService, indexService) =>
						accService + currService.harga_jual * curr.qty[indexService],
					0
				);
			}
			return acc + subtotalPerService;
		}, 0)
	);
	diskon = $state(0);
	ppn = $state(11);
	total = $derived(this.subtotal - this.diskon + (this.subtotal - this.diskon) * (this.ppn / 100));

	constructor() {
		this.addOrder();
	}

	addOrder() {
		const emptyOrder: ServiceOrder = {
			jenis_service: null,
			qty: [0],
			montir: null,
			service: null
		};

		this.orders.push(emptyOrder);
	}

	removeOrder(index: number) {
		this.orders = this.orders.filter((_, i) => i !== index);
	}
}

const SERVICE_ORDER_STATE_KEY = Symbol('@@service-order-state@@');

export function setServiceOrderState(): void {
	setContext(SERVICE_ORDER_STATE_KEY, new ServiceOrderStateClass());
}

export function getServiceOrderState(): ServiceOrderState {
	return getContext<ServiceOrderState>(SERVICE_ORDER_STATE_KEY);
}
