import type { StockOpname } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	const list: StockOpname[] = [
		{
			id_bengkel: '1',
			id_karyawan: '1',
			id_kartuStok: '1',
			id_stokOpname: '1',
			stok_sistem: 10,
			stok_fisik: 10,
			selisih: 0,
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_bengkel: '1',
			id_karyawan: '1',
			id_kartuStok: '2',
			id_stokOpname: '2',
			stok_sistem: 10,
			stok_fisik: 10,
			selisih: 0,
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		}
	];

	return { list };
};
