import type { Actions, PageServerLoad } from './$types';
import { PaketSchema, type Jasa, type Paket, type Sparepart } from '$lib/schema/general';

import { Effect, Schema } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async () => {
	const getPaket = retrieve<Paket[]>(fetch, '/paket');
	const getJasa = retrieve<Jasa[]>(fetch, '/jasa');

	const [list, listJasa] = await Effect.runPromise(
		Effect.all([getPaket, getJasa], { concurrency: 'unbounded' })
	);

	return {
		list: list.data,
		total_rows: list.total_rows,
		listJasa: listJasa.data
	};
};

export const actions: Actions = {
	'action:paket': async ({ request, fetch }) => {
		const data = await request.formData();
		const paket = JSON.parse(data.get('paket') as string) as Paket;
		const mode = data.get('mode');

		const jasaPaket = JSON.parse(data.get('jasa_paket') as string) as Jasa[];
		const sparepartPaket = JSON.parse(data.get('sparepart_paket') as string) as Sparepart[];

		console.log(jasaPaket);

		if (jasaPaket.length === 0 && sparepartPaket.length === 0) {
			return fail(400, {
				errors: { item: { error: 'Jasa atau Sparepart harus dipilih.', _tag: 'Type' } }
			});
		}

		const FormPaketSchema = Schema.Struct({
			...PaketSchema.omit('created_at', 'deleted_at', 'updated_at', 'jasa_paket', 'sparepart_paket')
				.fields,
			jasa_paket: Schema.Array(Schema.String),
			sparepart_paket: Schema.Array(Schema.String)
		});

		const payload = {
			...paket,
			jasa_paket: jasaPaket.map((item) => item.id_jasa),
			sparepart_paket: sparepartPaket.map((item) => item.kode_sparepart)
		};

		const decoded = decodeForm<typeof FormPaketSchema.Type, typeof FormPaketSchema.Encoded>(
			FormPaketSchema,
			payload
		);

		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = launch(fetch, '/paket', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify(payload)
		});

		const response = Effect.runPromise(action);
		return response;
	},
	'delete:paket': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id') as string;

		const action = launch(fetch, `/paket/${id}`, {
			method: 'DELETE'
		});

		const response = await Effect.runPromise(action);
		return response;
	}
};
