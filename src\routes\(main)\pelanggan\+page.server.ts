import type { Actions, PageServerLoad } from './$types';
import { CustomerSchema, type Customer } from '$lib/schema/general';

import { launch, retrieve } from '$lib/utils/fetch';
import { Effect } from 'effect';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ fetch, url }) => {
	const limit = url.searchParams.get('limit') ?? '10';
	const offset = url.searchParams.get('offset') ?? '1';
	const keyword = url.searchParams.get('keyword') ?? '';

	const getCustomer = retrieve<Customer[]>(
		fetch,
		`/customer?limit=${limit}&offset=${offset}&keyword=${keyword}`
	);
	const list = await Effect.runPromise(getCustomer);

	return { list: list.data, total_rows: list.total_rows };
};

export const actions: Actions = {
	'action:pelanggan': async ({ request, fetch }) => {
		const data = await request.formData();
		const pelanggan = JSON.parse(data.get('pelanggan') as string) as Customer;
		const mode = data.get('mode') as 'add' | 'edit';

		const pickedAttributes: (keyof Customer)[] = [
			'id_customer',
			'nama',
			'alamat',
			'no_telp',
			'tipe_customer',
			'keterangan',
			'username',
			'password'
		];
		if (mode === 'edit') pickedAttributes.pop();

		const FormCustomerSchema = CustomerSchema.pick(...pickedAttributes);

		const decoded = decodeForm<typeof FormCustomerSchema.Type, typeof FormCustomerSchema.Encoded>(
			FormCustomerSchema,
			pelanggan
		);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = launch(fetch, '/customer', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify(pelanggan)
		});

		const response = await Effect.runPromise(action);
		return response;
	},
	'delete:pelanggan': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id') as string;

		const action = launch(fetch, `/customer/${id}`, {
			method: 'DELETE'
		});

		const response = await Effect.runPromise(action);
		return response;
	}
};
