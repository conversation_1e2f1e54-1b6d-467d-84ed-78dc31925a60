<script lang="ts">
	import { getPokemon, getPokemonDetails, likePokemon } from './pokemon.remote';
	import type { Snippet } from 'svelte';

	import Icon from '@iconify/svelte';

	interface Pokemon {
		name: string;
		title: Snippet;
	}
	const { name, title }: Pokemon = $props();

	let pokemonList = getPokemon();
	let details = $derived(await getPokemonDetails(''));
	let selected = $state('bulbasaur');
</script>

{name}
{@render title()}

<div class="flex h-[80vh] w-[90vw] rounded-lg text-left">
	<ul
		class="scrollbar flex list-none flex-col gap-8 overflow-hidden overflow-y-auto px-8 py-4 text-6xl"
	>
		{#each await pokemonList as pokemon}
			{@const active = pokemon.name === selected}
			<li class="flex items-center justify-between gap-16">
				<button
					onclick={async () => {
						details = await getPokemonDetails(pokemon.url);
						selected = pokemon.name;
					}}
				>
					<span class={['capitalize transition-colors duration-300', active && 'text-[aqua]']}>
						{pokemon.name}
					</span>
				</button>

				<form
					{...likePokemon.enhance(async ({ data, submit }) => {
						const name = data.get('pokemon') as string;
						await submit().updates(
							getPokemon().withOverride((pokemon) =>
								pokemon.map((p) => (p.name === name ? { ...p, favorite: true } : p))
							)
						);
					})}
				>
					<input type="hidden" name="pokemon" value={pokemon.name} />
					<button class="grid place-content-center">
						{#if pokemon.favorite}
							<Icon icon="ri:star-fill" />
						{:else}
							<Icon icon="ri:star-line" />
						{/if}
					</button>
				</form>
			</li>
		{/each}
	</ul>

	<div class="mx-auto content-center">
		<img class="pixelated block aspect-square w-[600px]" src={details.image} alt={details.name} />
	</div>
</div>
