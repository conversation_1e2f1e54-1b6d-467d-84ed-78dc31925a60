<script lang="ts">
	import Login from './components/Login.svelte';
	import LoginHero from './components/LoginHero.svelte';
	import Register from './components/Register.svelte';
	import RegisterHero from './components/RegisterHero.svelte';

	let mode = $state<'login' | 'register'>('login');
</script>

<main class="bg-base-200 grid h-screen place-items-center overflow-auto">
	<div class="flex w-1/2 rounded-lg shadow-lg">
		<div
			class="border-base-200 text-primary flex w-5/12 flex-col items-stretch justify-center gap-2 border-e-2 border-e-gray-300 p-8 py-16"
		>
			{#if mode === 'login'}
				<LoginHero />
			{:else}
				<RegisterHero />
			{/if}
		</div>

		<div
			class="text-primary flex w-7/12 grow flex-col items-stretch justify-center gap-4 p-8 py-16"
		>
			{#if mode === 'login'}
				<Login />
			{:else}
				<Register />
			{/if}

			<div class="text-center text-sm">
				{#if mode === 'login'}
					<h3 class="text-neutral-600/50">
						Don't have an account?
						<button class="link text-secondary p-0" onclick={() => (mode = 'register')}>
							Register
						</button>
					</h3>
				{:else}
					<h3 class="text-neutral-600/50">
						Already have an account?
						<button class="link text-secondary p-0" onclick={() => (mode = 'login')}>
							Log In
						</button>
					</h3>
				{/if}
			</div>
		</div>
	</div>
</main>
