import type { Montir } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

interface Marker extends Montir {
	lngLat: [number, number];
	popupOpened: boolean;
}

export const load: PageServerLoad = async () => {
	const montir: Marker[] = [
		{
			id_montir: '1',
			id_bengkel: '1',
			nama: '<PERSON>',
			alamat: 'Jl. ABC No. 123',
			no_telp: '081234567890',
			spesialisasi: 'Service',
			status_montir: 'aktif',
			keterangan: null,
			lngLat: [112.654474, -7.30923],
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			popupOpened: false
		},
		{
			id_montir: '2',
			id_bengkel: '1',
			nama: '<PERSON>',
			alamat: 'Jl. ABC No. 123',
			no_telp: '081234567890',
			spesialisasi: 'Service',
			status_montir: 'aktif',
			keterangan: null,
			lngLat: [112.664474, -7.33923],
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			popupOpened: false
		},
		{
			id_montir: '3',
			id_bengkel: '1',
			nama: 'Walter White',
			alamat: 'Jl. ABC No. 123',
			no_telp: '081234567890',
			spesialisasi: 'Service',
			status_montir: 'nonaktif',
			keterangan: null,
			lngLat: [112.774474, -7.30923],
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			popupOpened: false
		}
	];

	return { montir };
};
