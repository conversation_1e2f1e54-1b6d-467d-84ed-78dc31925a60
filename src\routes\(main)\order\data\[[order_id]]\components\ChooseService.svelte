<script lang="ts">
	import { JasaSchema, type Jasa, type Paket, type Sparepart } from '$lib/schema/general';
	import type { ServiceType } from '$lib/schema/misc';
	import Icon from '@iconify/svelte';
	import { capitalize } from 'effect/String';
	import { getServiceOrderState } from '../serviceOrder/ServiceOrderState.svelte';
	import Currency from '$lib/inputs/Currency.svelte';

	let modal = $state<HTMLDialogElement>();

	let addService = $state({
		nama: '',
		harga: 0
	});

	interface IProps {
		list: {
			paket: Paket[];
			jasa: Jasa[];
			sparepart: Sparepart[];
		};
		mode?: ServiceType | 'general' | 'add';
		title?: string;
	}

	const { list, mode: initialMode = 'general', title = 'Pilih Service' }: IProps = $props();

	let mode = $state<ServiceType | 'general' | 'add'>(initialMode);

	const serviceOrderState = getServiceOrderState();
</script>

<button
	class="btn {initialMode === 'general' ? 'btn-sm' : 'btn-xs'} btn-outline btn-soft"
	onclick={() => {
		if (initialMode !== 'general') mode = initialMode;
		modal?.showModal();
	}}
>
	-- {title} -- <Icon icon="majesticons:chevron-down" />
</button>

<dialog class="modal" bind:this={modal}>
	<div class="modal-box text-primary">
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			{#if mode === 'general'}
				<h3 class="text-lg font-bold">Pilih Service</h3>
				<form method="dialog">
					<button class="btn btn-sm btn-circle btn-ghost">✕</button>
				</form>
			{:else if mode !== 'add'}
				<h3 class="text-lg font-bold">Pilih {capitalize(mode)}</h3>
				{#if initialMode === 'general'}
					<button class="btn btn-sm btn-circle btn-ghost" onclick={() => (mode = 'general')}>
						<Icon icon="majesticons:arrow-left" font-size="1.2em" />
					</button>
				{/if}
			{:else if mode === 'add'}
				<h3 class="text-lg font-bold">Tambah Service Manual</h3>
				<button class="btn btn-sm btn-circle btn-ghost" onclick={() => (mode = 'general')}>
					<Icon icon="majesticons:arrow-left" font-size="1.2em" />
				</button>
			{/if}
		</div>

		{#if mode === 'general'}
			<div class="flex flex-col gap-2">
				<button class="btn btn-outline w-full" onclick={() => (mode = 'paket')}>Paket</button>
				<button class="btn btn-outline w-full" onclick={() => (mode = 'jasa')}>Jasa</button>
				<button class="btn btn-outline w-full" onclick={() => (mode = 'sparepart')}>
					Sparepart
				</button>
				<button class="btn btn-outline btn-soft w-full" onclick={() => (mode = 'add')}>
					<Icon icon="majesticons:applications-add" /> Tambah Service
				</button>
			</div>
		{:else if mode === 'paket'}
			<label class="input input-sm mb-2 w-full">
				<Icon icon="majesticons:search-line" />
				<input type="search" name="keyword" id="keyword" class="grow" placeholder="Search..." />
			</label>

			<div class="flex max-h-36 flex-col gap-2 overflow-auto">
				{#each list.paket as paket (paket.nama_paket)}
					<button
						class="btn btn-outline btn-primary btn-soft w-full justify-start"
						onclick={() => {
							const lastIndex = serviceOrderState.orders.length - 1;

							serviceOrderState.orders[lastIndex].jenis_service = 'paket';
							serviceOrderState.orders[lastIndex].service = paket;
							serviceOrderState.orders[lastIndex].qty = [1];

							serviceOrderState.addOrder();
						}}
					>
						{paket.nama_paket}
					</button>
				{/each}
			</div>
		{:else if mode === 'jasa'}
			<label class="input input-sm mb-2 w-full">
				<Icon icon="majesticons:search-line" />
				<input type="search" name="keyword" id="keyword" class="grow" placeholder="Search..." />
			</label>

			<div class="flex max-h-36 flex-col gap-2 overflow-auto">
				{#each list.jasa as jasa (jasa.nama_jasa)}
					<button
						class="btn btn-outline btn-primary btn-soft w-full justify-start"
						onclick={() => {
							let serviceJasaIndex = serviceOrderState.orders.findIndex(
								(order) => order.jenis_service === 'jasa'
							);

							const lastIndex = serviceOrderState.orders.length - 1;

							if (serviceJasaIndex === -1) {
								serviceJasaIndex = lastIndex;

								serviceOrderState.orders[lastIndex].jenis_service = 'jasa';
								serviceOrderState.orders[lastIndex].service = [jasa];
								serviceOrderState.orders[lastIndex].qty = [1];

								serviceOrderState.addOrder();
							} else if (serviceOrderState.orders[serviceJasaIndex].service) {
								(serviceOrderState.orders[serviceJasaIndex].service as Jasa[]).push(jasa);
								serviceOrderState.orders[serviceJasaIndex].qty.push(1);
							}
						}}
					>
						{jasa.nama_jasa}
					</button>
				{/each}
			</div>
		{:else if mode === 'sparepart'}
			<label class="input input-sm mb-2 w-full">
				<Icon icon="majesticons:search-line" />
				<input type="search" name="keyword" id="keyword" class="grow" placeholder="Search..." />
			</label>

			<div class="flex max-h-36 flex-col gap-2 overflow-auto">
				{#each list.sparepart as sparepart (sparepart.nama_sparepart)}
					<button
						class="btn btn-outline btn-primary btn-soft w-full justify-start"
						onclick={() => {
							let sparepartsIndex = serviceOrderState.orders.findIndex(
								(order) => order.jenis_service === 'sparepart'
							);

							const lastIndex = serviceOrderState.orders.length - 1;

							if (sparepartsIndex === -1) {
								sparepartsIndex = lastIndex;

								serviceOrderState.orders[lastIndex].jenis_service = 'sparepart';
								serviceOrderState.orders[lastIndex].service = [sparepart];
								serviceOrderState.orders[lastIndex].qty = [1];

								serviceOrderState.addOrder();
							} else if (serviceOrderState.orders[sparepartsIndex].service) {
								(serviceOrderState.orders[sparepartsIndex].service as Sparepart[]).push(sparepart);
								serviceOrderState.orders[sparepartsIndex].qty.push(1);
							}
						}}
					>
						{sparepart.nama_sparepart}
					</button>
				{/each}
			</div>
		{:else if mode === 'add'}
			<label class="floating-label">
				<span>Input Service</span>
				<input
					type="text"
					name="input_service"
					id="input_service"
					class="input w-full"
					placeholder="Service"
					bind:value={addService.nama}
				/>
			</label>

			<br />

			<label class="floating-label">
				<span>Harga Service</span>
				<Currency
					name="harga_service"
					id="harga_service"
					bind:value={addService.harga}
					class="w-full"
				/>
			</label>
		{/if}

		<div class="modal-action">
			{#if mode === 'add'}
				<button
					class="btn btn-primary"
					onclick={() => {
						const lastIndex = serviceOrderState.orders.length - 1;

						serviceOrderState.orders[lastIndex].jenis_service = 'custom';
						serviceOrderState.orders[lastIndex].service = addService;
						serviceOrderState.orders[lastIndex].qty = [1];

						serviceOrderState.addOrder();
					}}>Simpan</button
				>
			{/if}
			<form method="dialog">
				<button class="btn" onclick={() => (mode = 'general')}>Close</button>
			</form>
		</div>
	</div>
</dialog>
