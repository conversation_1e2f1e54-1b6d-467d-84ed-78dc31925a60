<script lang="ts">
	import { type KategoriSparepart, type Satuan } from '$lib/schema/sparepart';
	import { enhance } from '$app/forms';
	import Icon from '@iconify/svelte';

	import Currency from '$lib/inputs/Currency.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';

	interface IProps {
		list_satuan: Satuan[];
	}
	const { list_satuan }: IProps = $props();

	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import Select from '$lib/inputs/Select.svelte';
	setUtilityModalState();
	const utilityModalState = getUtilityModalState();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} jasa ini?`,
				loader: 'action:jasa'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();
	let currencyReadonly = $derived(detailState.mode === 'view');

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();
</script>

<UtilityModal url="/kategorisparepart">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Kategori Sparepart</h2>
	{/snippet}

	{#snippet item({ item }: { item: KategoriSparepart })}
		{@const alreadySelected =
			detailState.kategoriSparepart?.id_kategori_sparepart === item.id_kategori_sparepart}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			class:btn-disabled={alreadySelected}
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Kategori sudah dipilih',
						type: 'error'
					});
					return;
				}
				detailState.kategoriSparepart = item;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama_kategori}
		</button>
	{/snippet}
</UtilityModal>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Sparepart</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<div class="flex items-center gap-4" class:hidden={detailState.mode === 'view'}>
			{#if detailState.kategoriSparepart}
				<p class="text-sm font-light">
					Kategori Sparepart : <span class="font-semibold">
						{detailState.kategoriSparepart.nama_kategori}</span
					>
				</p>
			{/if}

			<button
				class="btn btn-soft btn-primary btn-sm {!detailState.kategoriSparepart
					? 'w-full'
					: 'w-fit'}"
				onclick={() => utilityModalState.modal?.showModal()}
			>
				{#if !detailState.kategoriSparepart}
					Tentukan Kategori Sparepart
				{/if}
				<Icon icon="mdi:book-open-page-variant-outline" />
			</button>
		</div>

		<ValidationError name="id_kategori_sparepart" />

		{#if detailState.mode === 'view' && detailState.body.id_kategori_sparepart}
			<label for="kategori_sparepart" class="floating-label mb-4">
				<span>Kategori Sparepart</span>
				<input
					type="text"
					name="kategori_sparepart"
					id="kategori_sparepart"
					class="input w-full border-dashed"
					bind:value={detailState.body.id_kategori_sparepart.nama_kategori}
					placeholder="Kategori Sparepart"
					readonly
				/>
			</label>
		{/if}

		<div class="divider"></div>

		<label
			for="kode_sparepart"
			class="floating-label mb-4"
			class:hidden={detailState.mode === 'add'}
		>
			<span>Kode Sparepart</span>
			<input
				type="text"
				name="kode_sparepart"
				id="kode_sparepart"
				class="input w-full"
				bind:value={detailState.body.kode_sparepart}
				placeholder="Kode Sparepart"
				disabled
			/>
		</label>

		<label for="nama_sparepart" class="floating-label">
			<span>Nama Sparepart</span>
			<input
				type="text"
				name="nama_sparepart"
				id="nama_sparepart"
				class="input w-full"
				bind:value={detailState.body.nama_sparepart}
				placeholder="Nama Sparepart"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nama_sparepart" />
		</label>

		<br />

		<label for="keterangan" class="floating-label">
			<span>Deskripsi Sparepart</span>
			<textarea
				name="keterangan"
				id="keterangan"
				class="textarea w-full"
				bind:value={detailState.body.keterangan}
				placeholder="Deskripsi Sparepart"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			></textarea>
			<ValidationError name="keterangan" />
		</label>

		<br />

		<label for="merk_motor" class="floating-label">
			<span>Merk Kendaraan</span>
			<input
				type="text"
				name="merk_motor"
				id="merk_motor"
				class="input w-full"
				bind:value={detailState.body.merk_motor}
				placeholder="Merk Kendaraan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="merk_motor" />
		</label>

		<br />

		<label for="jenis_motor" class="floating-label">
			<span>Jenis Kendaraan</span>
			<input
				type="text"
				name="jenis_motor"
				id="jenis_motor"
				class="input w-full"
				bind:value={detailState.body.jenis_motor}
				placeholder="Jenis Kendaraan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="jenis_motor" />
		</label>

		<div class="divider"></div>

		<div class="grid grid-cols-2 items-center gap-2">
			<label for="stok_barang" class="floating-label">
				<span>Jumlah Stok</span>
				<input
					type="number"
					name="stok_barang"
					id="stok_barang"
					class="input w-full"
					bind:value={detailState.body.stok_barang}
					placeholder="Jumlah Stok"
					readonly={detailState.mode === 'view'}
					class:border-dashed={detailState.mode === 'view'}
				/>
				<ValidationError name="stok_barang" />
			</label>

			<label for="stok_minimum" class="floating-label">
				<span>Stok Minimum</span>
				<input
					type="number"
					name="stok_minimum"
					id="stok_minimum"
					class="input w-full"
					bind:value={detailState.body.stok_minimum}
					placeholder="Stok Minimum"
					readonly={detailState.mode === 'view'}
					class:border-dashed={detailState.mode === 'view'}
				/>
				<ValidationError name="stok_minimum" />
			</label>
		</div>

		<br />

		<label for="satuan" class="floating-label">
			<span>Satuan</span>
			{#key detailState.satuanSparepart}
				<Select
					class="select w-full"
					bind:value={detailState.satuanSparepart}
					name="satuan"
					id="satuan"
					matcher="nama_satuan"
					list={list_satuan}
				>
					{#each list_satuan as satuan}
						<option value={satuan.nama_satuan}>{satuan.nama_satuan}</option>
					{/each}
				</Select>
			{/key}

			<ValidationError name="nama_satuan" />
		</label>

		<br />

		<label for="harga_beli" class="floating-label">
			<span>Harga Beli</span>
			<Currency
				name="harga_beli"
				id="harga_beli"
				class="w-full {detailState.mode === 'view' ? 'border-dashed' : ''}"
				bind:value={detailState.body.harga_beli}
				bind:readonly={currencyReadonly}
			/>
			<ValidationError name="harga_beli" />
		</label>

		<br />

		<label for="harga_jual" class="floating-label">
			<span>Harga Jual</span>
			<Currency
				name="harga_jual"
				id="harga_jual"
				class="w-full {detailState.mode === 'view' ? 'border-dashed' : ''}"
				bind:value={detailState.body.harga_jual}
				bind:readonly={currencyReadonly}
			/>
			<ValidationError name="harga_jual" />
		</label>

		<br />

		<form
			action="?/action:sparepart"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="sparepart" value={JSON.stringify(detailState.body)} />
			<input
				type="hidden"
				name="kategori_sparepart"
				value={JSON.stringify(detailState.kategoriSparepart ?? { id_kategori_sparepart: '' })}
			/>
			<input
				type="hidden"
				name="satuan_sparepart"
				value={JSON.stringify(detailState.satuanSparepart ?? { id: '', nama_satuan: '' })}
			/>
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:jasa">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
