<script lang="ts">
	import Icon from '@iconify/svelte';
	import { enhance } from '$app/forms';
	import { formEnhancementBasic } from '$lib/utils/index';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	interface IProps {
		id: string;
	}

	const { id }: IProps = $props();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON>n<PERSON>rma<PERSON>',
				description: 'Apakah Anda yakin akan menghapus paket ini?',
				loader: 'delete:paket:' + id
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	action="?/delete:paket"
	method="post"
	use:enhance={() => formEnhancementBasic(confirmState, toastState)}
>
	<input type="hidden" name="id" value={id} />

	<div class="tooltip" data-tip="Hapus">
		<button
			type="button"
			class="btn btn-sm btn-outline btn-error uppercase"
			onclick={(e) => confirmation(e)}
		>
			<ConfirmLoader name="delete:paket:{id}">
				<Icon icon="mdi:trash-can" />
			</ConfirmLoader>
		</button>
	</div>
</form>
