<script lang="ts">
	import { enhance } from '$app/forms';

	import Currency from '$lib/inputs/Currency.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';
	import { tipeCustomer, type TipeCustomer } from '$lib/schema/literal';
	import { type Customer } from '$lib/schema/general';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: `<PERSON>pakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} pelanggan ini?`,
				loader: 'action:pelanggan'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	let selectedTipeAnggota = $state<TipeCustomer>('Individu');
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Pelanggan</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<label for="alamat" class="floating-label">
			<span>Username</span>
			<input
				type="text"
				name="username"
				id="username"
				class="input w-full"
				bind:value={detailState.body.username}
				placeholder="Username"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
				autocomplete="off"
			/>
			<ValidationError name="username" />
		</label>

		{#if detailState.mode === 'add'}
			<br />

			<label for="password" class="floating-label">
				<span>Password</span>
				<input
					type="password"
					name="password"
					id="password"
					class="input w-full"
					bind:value={detailState.body.password}
					placeholder="Password"
					autocomplete="off"
				/>
				<ValidationError name="password" />
			</label>
		{/if}

		<div class="divider"></div>

		<label for="nama" class="floating-label">
			<span>Nama Pelanggan</span>
			<input
				type="text"
				name="nama"
				id="nama"
				class="input w-full"
				bind:value={detailState.body.nama}
				placeholder="Nama Pelanggan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nama" />
		</label>

		<br />

		<label for="alamat" class="floating-label">
			<span>Alamat Pelanggan</span>
			<input
				type="text"
				name="alamat"
				id="alamat"
				class="input w-full"
				bind:value={detailState.body.alamat.jalan}
				placeholder="Alamat Pelanggan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="alamat" />
		</label>

		<br />

		<label for="no_telp" class="floating-label">
			<span>Nomor Telepon</span>
			<input
				type="tel"
				name="no_telp"
				id="no_telp"
				class="input w-full"
				bind:value={detailState.body.no_telp}
				placeholder="Nomor Telepon"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="no_telp" />
		</label>

		<br />

		<fieldset class="fieldset">
			<legend class="fieldset-legend">Tipe Pelanggan</legend>
			<div class="flex items-center gap-4">
				{#each tipeCustomer as tipe, i}
					<label class="label">
						<input
							type="radio"
							name="tipe_pelanggan_{i}"
							id="tipe_pelanggan_{i}"
							class="radio radio-sm"
							value={tipe}
							bind:group={selectedTipeAnggota}
							disabled={detailState.mode === 'view'}
						/>

						{tipe}
					</label>
				{/each}
			</div>
			<ValidationError name="tipe_customer" />
		</fieldset>

		<br />

		<label for="keterangan" class="floating-label">
			<span>Keterangan</span>
			<textarea
				name="keterangan"
				id="keterangan"
				class="textarea w-full"
				bind:value={detailState.body.keterangan}
				placeholder="Keterangan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			></textarea>
			<ValidationError name="keterangan" />
		</label>

		<br />

		<form
			action="?/action:pelanggan"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input
				type="hidden"
				name="pelanggan"
				value={JSON.stringify({
					...detailState.body,
					tipe_customer: selectedTipeAnggota
				} as Customer)}
			/>
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:pelanggan">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
