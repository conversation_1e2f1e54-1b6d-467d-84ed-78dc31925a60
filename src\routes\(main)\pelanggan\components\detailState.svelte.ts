import type { Customer } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;

	body: Customer;
	emptyBody: Customer;

	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();

	emptyBody: Customer = {
		id_customer: '',
		nama: '',
		alamat: {
			jalan: '',
			'rt/rw': '',
			kelurahan: '',
			kecamatan: '',
			kota: '',
			provinsi: ''
		},
		no_telp: '+62-',
		username: '',
		password: '',
		tipe_customer: 'Individu',
		keterangan: '',
		created_at: '',
		updated_at: null,
		deleted_at: null
	};

	body = $state<Customer>(this.emptyBody);

	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = this.emptyBody;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
