import type { PageServerLoad } from './$types';
import type { Jasa, Montir, Paket, Sparepart } from '$lib/schema/general';
import { _Invoice, type Invoice } from '$lib/schema/order';

import { Effect } from 'effect';
import { retrieve } from '$lib/utils/fetch';

export const load: PageServerLoad = async ({ fetch, params }) => {
	let invoice: Invoice = _Invoice;

	if (params.order_id) {
		const getInvoice = retrieve<Invoice>(fetch, `/invoice/${params.order_id}`);
		const _invoice = await Effect.runPromise(getInvoice);
		invoice = _invoice.data;
	}

	const list: {
		paket: Paket[];
		jasa: Jasa[];
		sparepart: Sparepart[];
	} = {
		paket: [],
		jasa: [],
		sparepart: []
	};

	const listMontir: Montir[] = [
		{
			id_montir: '1',
			id_bengkel: '1',
			nama: '<PERSON>',
			alamat: 'Jl. ABC No. 123',
			no_telp: '081234567890',
			spesialisasi: 'Service',
			status_montir: 'aktif',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		}
	];

	return { invoice, list, listMontir };
};
