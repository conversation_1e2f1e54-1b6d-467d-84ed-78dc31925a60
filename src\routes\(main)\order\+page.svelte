<script lang="ts">
	import { goto } from '$app/navigation';
	import { shortcut } from '$lib/actions/shortcut.svelte';
</script>

{#snippet menu(
	num: number,
	href: string,
	title: string,
	color: string = '',
	textColor: string = ''
)}
	<a {href}>
		<button
			class="btn relative grid h-full w-full place-items-center gap-4 rounded px-4 py-2 hover:opacity-90 {color}"
		>
			<div class="absolute">
				<h2 class=" text-9xl text-white tabular-nums">{num}</h2>
				<div class="text-base-200 opacity-30">
					press <div class="kbd kbd-lg text-base-content">{num}</div>
				</div>
			</div>
			<h3 class="absolute z-10 text-4xl uppercase {textColor}">{title}</h3>
		</button>
	</a>
{/snippet}

<div class="relative grid h-full place-items-center p-8">
	<!-- svelte-ignore a11y_autofocus -->
	<input
		use:shortcut={[
			{ key: '1', callback: () => goto('/order/list/') },
			{ key: '2', callback: () => goto('/order/list/antrian') },
			{ key: '3', callback: () => goto('/order/list/proses') },
			{ key: '4', callback: () => goto('/order/list/selesai') }
		]}
		class="absolute inset-0 cursor-default focus:outline-none"
		autofocus
		readonly
	/>
	<div class="grid h-full w-full grid-cols-2 items-stretch gap-6">
		{@render menu(1, '/order/list', 'Order', 'bg-blue-400/50', 'text-blue-700')}
		{@render menu(2, '/order/list/antrian', 'Antrian', 'bg-rose-400/50', 'text-rose-700')}
		{@render menu(3, '/order/list/proses', 'Proses', 'bg-amber-400/50	', 'text-amber-700')}
		{@render menu(4, '/order/list/selesai', 'Selesai', 'bg-emerald-400/50', 'text-emerald-700')}
	</div>
</div>
