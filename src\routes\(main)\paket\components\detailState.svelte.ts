import type { Jasa, Paket, Sparepart } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Paket;
	emptyBody: Paket;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;

	jasaPaket: Jasa[];
	sparepartPaket: Sparepart[];
	totalHargaItem: number;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();

	emptyBody: Paket = {
		id_paket: '',
		nama_paket: '',
		harga: 0,
		durasi_estimasi: 0,
		keterangan: null,
		created_at: '',
		updated_at: '',
		deleted_at: '',

		jasa_paket: [],
		sparepart_paket: []
	};

	body = $state<Paket>(this.emptyBody);
	mode = $state<'add' | 'view' | 'edit'>('add');

	jasaPaket = $state<Jasa[]>([]);
	sparepartPaket = $state<Sparepart[]>([]);
	totalHargaItem = $derived(
		(this.jasaPaket ?? [])?.reduce((acc, curr) => acc + curr.harga, 0) +
			(this.sparepartPaket ?? [])?.reduce((acc, curr) => acc + curr.harga_jual, 0)
	);

	addNewBody() {
		this.body = this.emptyBody;
		this.jasaPaket = [];
		this.sparepartPaket = [];
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
