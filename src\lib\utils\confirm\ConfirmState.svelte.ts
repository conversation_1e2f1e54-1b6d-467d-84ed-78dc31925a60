import { getContext, setContext } from 'svelte';

export interface ConfirmState {
	title: string;
	description: string;

	asking: (
		option: { title?: string; description: string; loader?: string },
		callback: () => void
	) => void;
	callback: () => void;
	accepted: () => void;
	declined: () => void;

	submitForm: (e: Event) => void;

	modal: HTMLDialogElement | undefined;
	loader: string;
}

export default class ConfirmStateClass implements ConfirmState {
	title = $state('');
	description = $state('');
	loader = $state('');

	modal = $state<HTMLDialogElement>();
	callback = $state(() => {});

	asking(option: { title?: string; description: string; loader?: string }, callback: () => void) {
		this.modal?.showModal();

		this.title = option.title ?? 'Konfirmasi';
		this.description = option.description;

		this.callback = callback;

		this.loader = option.loader ?? '';
	}

	accepted() {
		this.callback();
		this.modal?.close();
	}

	declined() {
		this.modal?.close();
		this.loader = '';
	}

	submitForm(e: Event) {
		const target = e.target as HTMLElement;
		const form = target.closest('form') as HTMLFormElement;
		form?.requestSubmit();
	}
}

const CONFIRM_STATE_KEY = Symbol('@@confirm-state@@');

export function setConfirmState(): void {
	setContext(CONFIRM_STATE_KEY, new ConfirmStateClass());
}

export function getConfirmState(): ConfirmState {
	return getContext<ConfirmState>(CONFIRM_STATE_KEY);
}
