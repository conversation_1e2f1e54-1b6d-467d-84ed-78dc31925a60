<script lang="ts">
	import Icon from '@iconify/svelte';
	import { getConfirmState } from './ConfirmState.svelte';

	let modal = $state<HTMLDialogElement>();

	const confirmState = getConfirmState();
	$effect(() => {
		confirmState.modal = modal;
	});
</script>

<dialog class="modal" bind:this={modal}>
	<div class="modal-box">
		<form method="dialog">
			<button
				class="btn btn-sm btn-circle btn-ghost absolute top-2 right-2"
				type="button"
				onclick={() => confirmState.declined()}>✕</button
			>
		</form>

		<div class="text-secondary grid place-items-center p-4 text-center">
			<Icon icon="majesticons:exclamation-circle-line" font-size="4rem" />
		</div>

		<p class="text-center font-semibold tracking-wide">{confirmState.description}</p>

		<br />

		<div class="flex items-center justify-center gap-2">
			<button
				type="button"
				class="btn btn-md btn-error btn-ghost text-error-content"
				onclick={() => confirmState.declined()}
			>
				Tidak
				<Icon icon="mdi:close" />
			</button>

			<button
				type="button"
				class="btn btn-md btn-success btn-outline"
				onclick={() => confirmState.accepted()}
			>
				<Icon icon="mdi:check" /> Ya
			</button>
		</div>
	</div>
</dialog>
