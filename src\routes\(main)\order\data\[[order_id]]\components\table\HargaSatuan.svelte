<script lang="ts">
	import type { <PERSON><PERSON>, <PERSON><PERSON>, Sparepart } from '$lib/schema/general';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import type { CustomService } from '../../serviceOrder/ServiceOrderState.svelte';

	interface IProps {
		body: {
			jenis_service: 'paket' | 'jasa' | 'sparepart' | 'custom' | null;
			qty: number[];
			service: Paket | Jasa[] | Sparepart[] | CustomService | null;
		};
	}

	const { body }: IProps = $props();
</script>

<div class="flex flex-col gap-1">
	{#if body.jenis_service === 'jasa'}
		{#each body.service as Jasa[] as service, i}
			<p class="mb-1">{rupiah(service.harga)}</p>
		{/each}
	{:else if body.jenis_service === 'sparepart'}
		{#each body.service as Sparepart[] as service, i}
			<p class="mb-1">{rupiah(service.harga_jual)}</p>
		{/each}
	{:else if body.jenis_service === 'custom' || body.jenis_service === 'paket'}
		<p>{rupiah((body.service as CustomService).harga)}</p>
	{/if}

	{#if body.jenis_service === 'jasa' || body.jenis_service === 'sparepart'}
		<p>&nbsp;</p>
	{/if}
</div>
