import type { Actions, PageServerLoad } from './$types';

import { SparepartSchema, type Sparepart } from '$lib/schema/general';
import {
	KategoriSparepartSchema,
	SatuanSchema,
	type KategoriSparepart,
	type KategoriSparepartEncoded,
	type Satuan,
	type SatuanEncoded
} from '$lib/schema/sparepart';

import { Effect, Schema } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';
import { env } from '$env/dynamic/public';

export const load: PageServerLoad = async ({ fetch }) => {
	const getSparepart = retrieve<Sparepart[]>(fetch, '/sparepart');
	const getKategori = retrieve<KategoriSparepart[]>(fetch, '/kategorisparepart');
	const getSatuan = retrieve<Satuan[]>(fetch, '/satuan');

	const [list, kategori, satuan] = await Effect.runPromise(
		Effect.all([getSparepart, getKategori, getSatuan], { concurrency: 'unbounded' })
	);

	return {
		list: list.data,
		total_rows: list.total_rows,
		kategori: kategori.data,
		kategori_total_rows: kategori.total_rows,
		satuan: satuan.data,
		satuan_total_rows: satuan.total_rows
	};
};

export const actions: Actions = {
	'action:sparepart': async ({ request, fetch, locals }) => {
		const data = await request.formData();
		const sparepart = JSON.parse(data.get('sparepart') as string) as Sparepart;
		const kategoriSparepart = JSON.parse(
			data.get('kategori_sparepart') as string
		) as KategoriSparepart;
		const satuan = JSON.parse(data.get('satuan_sparepart') as string) as Satuan;

		const decodedKategoriSparepart = decodeForm<KategoriSparepart, KategoriSparepartEncoded>(
			KategoriSparepartSchema,
			kategoriSparepart
		);

		if ('error' in decodedKategoriSparepart)
			return fail(400, { errors: decodedKategoriSparepart.errors });

		const decodedSatuan = decodeForm<Satuan, SatuanEncoded>(SatuanSchema, satuan);
		if ('error' in decodedSatuan) return fail(400, { errors: decodedSatuan.errors });

		//

		const FormSparepartSchema = Schema.Struct({
			...SparepartSchema.omit(
				'created_at',
				'deleted_at',
				'updated_at',
				'id_kategori_sparepart',
				'id_satuan',
				'keterangan_hapus'
			).fields,
			id_kategori_sparepart: Schema.String,
			id_satuan: Schema.Number
		});

		type FormSparepart = Schema.Schema.Type<typeof FormSparepartSchema>;
		type FormSparepartEncoded = Schema.Schema.Encoded<typeof FormSparepartSchema>;

		const decodedSparepart = decodeForm<FormSparepart, FormSparepartEncoded>(FormSparepartSchema, {
			...sparepart,
			id_kategori_sparepart: decodedKategoriSparepart.id_kategori_sparepart,
			id_satuan: decodedSatuan.id,
			id_bengkel: env.PUBLIC_ID_BENGKEL,
			id_karyawan: locals.auth.id
		});
		if ('error' in decodedSparepart) return fail(400, { errors: decodedSparepart.errors });

		//

		const mode = data.get('mode');

		const action = launch(fetch, '/sparepart', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify(decodedSparepart)
		});

		const response = await Effect.runPromise(action);
		return response;
	},
	'delete:sparepart': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id') as string;

		const action = launch(fetch, `/sparepart/${id}`, {
			method: 'DELETE'
		});

		const response = await Effect.runPromise(action);
		return response;
	}
};
