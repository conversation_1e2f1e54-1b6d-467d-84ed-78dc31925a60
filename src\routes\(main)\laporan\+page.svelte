<script lang="ts">
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import { BarY, Dot, Line, Plot } from 'svelteplot';

	const { data } = $props();
</script>

<div class="tabs tabs-border">
	<input type="radio" name="dashboard-tab" class="tab" aria-label="Laporan Statistik" checked />
	<div class="tab-content border-base-300 bg-base-100 p-4">
		<div class="stats bg-base-100 border-base-300 border">
			<div class="stat">
				<div class="stat-title">Total Penjualan</div>
				<div class="stat-value">{rupiah(1000000)}</div>
				<div class="stat-actions">
					<button class="btn btn-xs btn-primary btn-soft"> Harian </button>
					<button class="btn btn-xs btn-primary btn-soft"> Bulanan </button>
					<button class="btn btn-xs btn-primary btn-soft"> Mingguan </button>
					<button class="btn btn-xs btn-primary btn-soft"> Total </button>
				</div>
			</div>

			<div class="stat">
				<div class="stat-title">Pemasukan</div>
				<div class="stat-value">{rupiah(1000000)}</div>
				<div class="stat-actions">
					<button class="btn btn-xs btn-primary btn-soft"> Harian </button>
					<button class="btn btn-xs btn-primary btn-soft"> Bulanan </button>
					<button class="btn btn-xs btn-primary btn-soft"> Mingguan </button>
					<button class="btn btn-xs btn-primary btn-soft"> Total </button>
				</div>
			</div>
		</div>

		<div class="divider"></div>

		<h3 class="mb-2 text-sm font-semibold">Statistik Penjualan</h3>
		<Plot>
			<BarY data={data.penjualan} x="time" y="value" fill="primary" fillOpacity={0.5} stroke="gray"
			></BarY>
		</Plot>

		<div class="divider"></div>

		<h3 class="mb-2 text-sm font-semibold">Statistik Pemasukan</h3>
		<Plot grid>
			<Dot data={data.penjualan} x="time" y="value" fill strokeWidth={4} stroke="primary"></Dot>
			<Line data={data.penjualan} x="time" y="value" opacity={0.5} stroke="gray"></Line>
		</Plot>
	</div>

	<input type="radio" name="dashboard-tab" class="tab" aria-label="Laporan Bengkel" />
	<div class="tab-content border-base-300 bg-base-100 p-4">
		<div>Laporan Bengkel</div>
	</div>
</div>
