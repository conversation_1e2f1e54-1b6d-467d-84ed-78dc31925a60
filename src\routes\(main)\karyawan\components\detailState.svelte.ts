import { env } from '$env/dynamic/public';
import type { <PERSON><PERSON><PERSON> } from '$lib/schema/general';
import { jabatan } from '$lib/schema/literal';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: <PERSON><PERSON><PERSON>;
	emptyBody: <PERSON><PERSON><PERSON>;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();

	emptyBody: Karyawan = {
		id_karyawan: '',

		nama: '',
		alamat: {
			jalan: '',
			'rt/rw': '',
			kelurahan: '',
			kecamatan: '',
			kota: '',
			provinsi: ''
		},
		no_telp: '+62-',
		jabatan: jabatan[0],
		status: '',
		keterangan: '',
		created_at: '',
		updated_at: '',
		deleted_at: null,

		username: '',
		password: '',
		foto: '',

		// TEMPORARY
		bengkel: {
			id_bengkel: env.PUBLIC_ID_BENGKEL,
			nama_bengkel: '',
			alamat: '',
			no_telp: '',
			keterangan: '',
			created_at: '',
			updated_at: '',
			deleted_at: null
		}
	};

	body = $state<Karyawan>(this.emptyBody);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = this.emptyBody;
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
