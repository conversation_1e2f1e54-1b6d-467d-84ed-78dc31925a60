import type { KategoriSparepart, Sparepart } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const opnameId = params.id;

	const list: Sparepart[] = [
		{
			kode_sparepart: '1',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: '<PERSON><PERSON>',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan <PERSON>',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '2',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Ban Depan',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Ban Depan',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '3',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Ban Belakang',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Ban Belakang',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '4',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Aki',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Aki',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '5',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Kopling',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Kopling',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '6',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Rem',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Rem',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '7',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Tangki',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Tangki',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '8',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Kopling',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Kopling',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '9',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Kopling',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Kopling',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '10',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Kopling',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Kopling',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		},
		{
			kode_sparepart: '11',
			id_kartustok: 1,
			id_satuan: '1',
			id_kategori_sparepart: '1',
			nama_sparepart: 'Kopling',
			stok_minimum: 10,
			harga_beli: 10000,
			harga_jual: 12000,
			uprate: null,
			keterangan: 'Keterangan Kopling',
			link_gambar_sparepart: 'https://via.placeholder.com/150',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan_hapus: null
		}
	];

	const kategori: KategoriSparepart[] = [
		{
			id_kategori_sparepart: '1',
			nama: 'Busi',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '2',
			nama: 'Ban',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '3',
			nama: 'Aki',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '4',
			nama: 'Kopling',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '5',
			nama: 'Rem',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '6',
			nama: 'Tangki',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '7',
			nama: 'Kopling',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '8',
			nama: 'Kopling',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		},
		{
			id_kategori_sparepart: '9',
			nama: 'Kopling',
			keterangan: null,
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null
		}
	];
	return { opnameId, list, kategori };
};
