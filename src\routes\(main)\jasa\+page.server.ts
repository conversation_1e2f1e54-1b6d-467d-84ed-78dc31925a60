import type { Actions, PageServerLoad } from './$types';
import { JasaSchema, type Jasa, type JasaEncoded } from '$lib/schema/general';

import { Effect } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';

export const load: PageServerLoad = async ({ fetch, url }) => {
	const limit = url.searchParams.get('limit') ?? '20';
	const offset = url.searchParams.get('offset') ?? '1';
	const keyword = url.searchParams.get('keyword') ?? '';

	const getJasa = retrieve<Jasa[]>(
		fetch,
		`/jasa?limit=${limit}&offset=${offset}&keyword=${keyword}`
	);
	const list = await Effect.runPromise(getJasa);

	return { list: list.data, total_rows: list.total_rows };
};

export const actions: Actions = {
	'action:jasa': async ({ request, fetch }) => {
		const data = await request.formData();
		const jasa = JSON.parse(data.get('jasa') as string) as Jasa;
		const mode = data.get('mode');

		const decoded = decodeForm<Jasa, JasaEncoded>(JasaSchema, jasa);
		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		const action = launch(fetch, '/jasa', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify(jasa)
		});

		const response = Effect.runPromise(action);
		return response;
	},
	'delete:jasa': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id');

		const action = launch(fetch, `/jasa/${id}`, {
			method: 'DELETE'
		});

		const response = Effect.runPromise(action);
		return response;
	}
};
