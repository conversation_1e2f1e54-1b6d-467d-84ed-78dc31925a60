import type { Jasa } from '$lib/schema/general';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	body: Jasa;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	body = $state<Jasa>({
		id_jasa: '',
		nama_jasa: '',
		harga: 0,
		durasi_estimasi: 0,
		keterangan: null,
		created_at: '',
		updated_at: null,
		deleted_at: null
	});
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = {
			id_jasa: '',
			nama_jasa: '',
			harga: 0,
			durasi_estimasi: 0,
			keterangan: '',
			created_at: '',
			updated_at: null,
			deleted_at: null
		};
	}
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
