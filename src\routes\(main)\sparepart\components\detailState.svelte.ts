import type { Sparepart } from '$lib/schema/general';
import type { KategoriSparepart, Satuan } from '$lib/schema/sparepart';
import { getContext, setContext } from 'svelte';

export interface DetailState {
	modal: HTMLDialogElement | undefined;
	emptyBody: Sparepart;
	body: Sparepart;
	mode: 'add' | 'view' | 'edit';

	addNewBody(): void;

	kategoriSparepart: KategoriSparepart | null;
	satuanSparepart: Satuan | null;
}

export default class DetailStateClass implements DetailState {
	modal = $state<HTMLDialogElement>();
	emptyBody: Sparepart = {
		kode_sparepart: '',
		nama_sparepart: '',

		harga_beli: 0,
		harga_jual: 0,
		stok_minimum: 0,
		stok_barang: 0,
		keterangan: null,

		created_at: '',
		updated_at: null,
		deleted_at: null,
		keterangan_hapus: null,

		id_kategori_sparepart: null,
		id_satuan: null,

		merk_motor: null,
		jenis_motor: null,

		id_karyawan: '',
		id_bengkel: ''
	};
	body = $state<Sparepart>(this.emptyBody);
	mode = $state<'add' | 'view' | 'edit'>('add');

	addNewBody() {
		this.body = this.emptyBody;
	}

	kategoriSparepart = $state<KategoriSparepart | null>(null);
	satuanSparepart = $state<Satuan | null>(null);
}

const DETAIL_STATE_KEY = Symbol('@@detail-state@@');

export function setDetailState(): void {
	setContext(DETAIL_STATE_KEY, new DetailStateClass());
}

export function getDetailState(): DetailState {
	return getContext<DetailState>(DETAIL_STATE_KEY);
}
