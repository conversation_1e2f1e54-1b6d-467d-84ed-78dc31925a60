<script lang="ts">
	import type { Jasa, Paket, Sparepart } from '$lib/schema/general';
	import type { CustomService } from '../../serviceOrder/ServiceOrderState.svelte';
	import Icon from '@iconify/svelte';
	import ChooseService from '../ChooseService.svelte';

	interface IProps {
		body: {
			jenis_service: 'paket' | 'jasa' | 'sparepart' | 'custom' | null;
			qty: number[];
			service: Paket | Jasa[] | Sparepart[] | CustomService | null;
		};
		list: {
			paket: Paket[];
			jasa: Jasa[];
			sparepart: Sparepart[];
		};
	}

	const { body, list }: IProps = $props();
</script>

{#if !body.jenis_service}
	<ChooseService {list} />
{:else if body.jenis_service === 'paket'}
	{@const service = body.service as Paket}

	<div class="flex items-center justify-between gap-2">
		<div class="text-primary">
			<h3 class="font-semibold">Paket {service.nama_paket}</h3>

			<ol class="list-inside list-decimal ps-1">
				{#each service.jasa_paket as detail}
					<li>{detail}</li>
				{/each}
			</ol>
		</div>
	</div>
{:else if body.jenis_service === 'jasa'}
	{@const services = body.service as Jasa[]}

	<div class="text-primary flex flex-col gap-1">
		<h3 class="mb-1 font-semibold">Jasa</h3>

		<ol class="flex list-inside list-decimal flex-col gap-1 ps-1">
			{#each services as jasa}
				<li class="mb-1">{jasa.nama_jasa}</li>
			{/each}
		</ol>

		<ChooseService {list} mode="jasa" title="Tambah Jasa" />
	</div>

	<br />
{:else if body.jenis_service === 'sparepart'}
	{@const services = body.service as Sparepart[]}
	<div class="text-primary flex flex-col gap-1">
		<h3 class="mb-1 font-semibold">Sparepart</h3>

		<ol class="flex list-inside list-decimal flex-col gap-1 ps-1">
			{#each services as sparepart}
				<li class="mb-1">{sparepart.nama_sparepart}</li>
			{/each}
		</ol>

		<ChooseService {list} mode="sparepart" title="Tambah Sparepart" />
	</div>

	<br />
{:else if body.jenis_service === 'custom'}
	<div class="text-primary">
		<h3 class="mb-1 font-semibold">{(body.service as CustomService).nama}</h3>
	</div>
{:else}
	<div>{body.jenis_service}</div>
{/if}
