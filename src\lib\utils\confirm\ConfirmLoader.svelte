<script lang="ts">
	import type { Snippet } from 'svelte';
	import { getConfirmState } from './ConfirmState.svelte';
	import { cn } from '..';

	interface IProps {
		name: string;
		children: Snippet;
		class?: string;
	}
	const { name, children, class: className }: IProps = $props();

	const confirmState = getConfirmState();
</script>

{#if confirmState.loader === name}
	<span class={cn('loading loading-spinner loading-xs', className ?? '')}></span>
{:else}
	{@render children()}
{/if}
