<script lang="ts">
	import type { <PERSON><PERSON>, <PERSON>et, Sparepart } from '$lib/schema/general';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import type { CustomService } from '../../serviceOrder/ServiceOrderState.svelte';

	interface IProps {
		body: {
			jenis_service: 'paket' | 'jasa' | 'sparepart' | 'custom' | null;
			qty: number[];
			service: Paket | Jasa[] | Sparepart[] | CustomService | null;
		};
	}

	const { body }: IProps = $props();
</script>

{#if body.jenis_service === 'paket' || body.jenis_service === 'custom'}
	{rupiah((body.service as Paket).harga)}
{:else if body.jenis_service === 'jasa'}
	{@const harga = (body.service as Jasa[]).reduce(
		(acc, curr, index) => acc + curr.harga * body.qty[index],
		0
	)}
	{rupiah(harga)}
{:else if body.jenis_service === 'sparepart'}
	{@const harga = (body.service as Sparepart[]).reduce(
		(acc, curr, index) => acc + curr.harga_jual * body.qty[index],
		0
	)}
	{rupiah(harga)}
{/if}
