import { Either, Schema, Effect, ParseResult } from 'effect';

//To make Object.groupBy() working
import 'core-js/proposals/array-grouping-v2';

export const decodeArrayRaw = <Type, Encoded>(
	schema: Schema.Schema<Type, Encoded>,
	arr: unknown[]
) => {
	const decode = Schema.decodeUnknownEither(schema, { errors: 'all' });
	const decoded = arr.map((item) => {
		const decoded = decode(item);

		if (Either.isRight(decoded)) {
			return decoded.right;
		} else if (Either.isLeft(decoded)) {
			return { error: true, errors: ParseResult.ArrayFormatter.formatErrorSync(decoded.left) };
		}
	});

	return decoded;
};

export type DecodeFormValueErrors = Partial<Record<PropertyKey, ParseResult.ArrayFormatterIssue[]>>;
export const decodeForm = <T, Encoded>(
	schema: Schema.Schema<T, Encoded>,
	arr: unknown
): T | { error: boolean; errors: DecodeFormValueErrors } => {
	const decode = Schema.decodeUnknownEither(schema, { errors: 'all' });
	const decoded = decode(arr);

	if (Either.isRight(decoded)) {
		return decoded.right as T;
	} else {
		const errorsFormatted = ParseResult.ArrayFormatter.formatErrorSync(decoded.left);
		const errors = Object.groupBy(errorsFormatted, (item) => item.path[0]);

		return { error: true, errors };
	}
};

export type EncodeFormValueErrors = Partial<Record<PropertyKey, ParseResult.ArrayFormatterIssue[]>>;
export const encodeFormValueRaw = <T, Encoded>(
	schema: Schema.Schema<T, Encoded>,
	arr: unknown
): Encoded | { error: boolean; errors: EncodeFormValueErrors } => {
	const encode = Schema.encodeUnknownEither(schema, { errors: 'all' });
	const encoded = encode(arr);

	if (Either.isRight(encoded)) {
		return encoded.right as Encoded;
	} else {
		const errorsFormatted = ParseResult.ArrayFormatter.formatErrorSync(encoded.left);
		const errors = Object.groupBy(errorsFormatted, (item) => item.path[0]);

		return { error: true, errors };
	}
};

export const validateArrayRaw = <Type, Encoded>(
	arrayEffect: Effect.Effect<Type[], unknown>,
	schema: Schema.Schema<Type, Encoded>
) =>
	arrayEffect.pipe(
		Effect.flatMap((array) => {
			const decode = Schema.decodeUnknownEither(schema);
			const decoded = array.map((item) => {
				const decoded = decode(item);
				if (Either.isRight(decoded)) {
					return decoded.right;
				} else {
					console.log(decoded.left.message);
				}
			});

			return Effect.succeed(decoded);
		})
	);
