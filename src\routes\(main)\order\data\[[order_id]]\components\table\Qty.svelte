<script lang="ts">
	import type { J<PERSON>, Paket, Sparepart } from '$lib/schema/general';
	import {
		getServiceOrderState,
		type CustomService
	} from '../../serviceOrder/ServiceOrderState.svelte';
	import Icon from '@iconify/svelte';

	interface IProps {
		body: {
			jenis_service: 'paket' | 'jasa' | 'sparepart' | 'custom' | null;
			qty: number[];
			service: Paket | Jasa[] | Sparepart[] | CustomService | null;
		};
		index: number;
	}

	const { body, index }: IProps = $props();

	const serviceOrder = getServiceOrderState();
</script>

<div class="flex flex-col gap-1">
	{#if body.jenis_service}
		<div class="flex flex-col gap-1">
			{#each body.qty as qty, i}
				<div class="flex items-center gap-1">
					<input
						class="input input-xs w-16"
						bind:value={body.qty[i]}
						type="number"
						min="1"
						disabled={body.jenis_service === 'paket' || body.jenis_service === 'custom'}
					/>

					<div class="tooltip" data-tip="Hapus">
						<button
							class="btn btn-error btn-xs btn-outline"
							type="button"
							onclick={() => {
								if (body.jenis_service === 'paket' || body.jenis_service === 'custom')
									serviceOrder.removeOrder(index);
								else {
									if (body.qty.length <= 1) serviceOrder.removeOrder(index);
									else {
										body.qty.splice(i, 1);
										(body.service as Jasa[] | Sparepart[]).splice(i, 1);
									}
								}
							}}
						>
							<Icon icon="heroicons:trash-16-solid" font-size="1.2em" />
						</button>
					</div>
				</div>
			{/each}
		</div>
	{/if}

	{#if body.jenis_service === 'jasa' || body.jenis_service === 'sparepart'}
		<p>&nbsp;</p>
	{/if}
</div>
