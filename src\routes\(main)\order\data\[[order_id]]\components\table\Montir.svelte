<script lang="ts">
	import type { Montir } from '$lib/schema/general';
	import { getServiceOrderState } from '../../serviceOrder/ServiceOrderState.svelte';

	import Icon from '@iconify/svelte';

	interface IProps {
		body: {
			jenis_service: 'paket' | 'jasa' | 'sparepart' | 'custom' | null;
			qty: number[];
			montir: Montir | null;
		};
		list: Montir[];
		index: number;
	}

	const { body, list, index }: IProps = $props();

	let modal = $state<HTMLDialogElement>();

	const serviceOrderState = getServiceOrderState();
</script>

{#if !body.montir}
	<button class="btn btn-sm btn-outline btn-soft" onclick={() => modal?.showModal()}>
		-- Pilih <PERSON> -- <Icon icon="majesticons:chevron-down" />
	</button>

	<dialog class="modal" bind:this={modal}>
		<div class="modal-box text-primary">
			<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
				<h3 class="text-lg font-bold">Pilih Montir</h3>
				<form method="dialog">
					<button class="btn btn-sm btn-circle btn-ghost">✕</button>
				</form>
			</div>

			<label class="input input-sm mb-2 w-full">
				<Icon icon="majesticons:search-line" />
				<input type="search" name="keyword" id="keyword" class="grow" placeholder="Search..." />
			</label>

			<div class="flex max-h-36 flex-col gap-2 overflow-auto">
				{#each list as montir (montir.id_montir)}
					<button
						class="btn btn-outline btn-primary btn-soft w-full justify-start"
						onclick={() => {
							serviceOrderState.orders[index].montir = montir;
						}}
					>
						{montir.nama} ({montir.id_montir})
					</button>
				{/each}
			</div>
		</div>
	</dialog>
{:else}
	<div>{body.montir.nama} ({body.montir.id_montir})</div>
{/if}
