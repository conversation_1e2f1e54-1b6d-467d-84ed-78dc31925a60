<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { data } = $props();
</script>

<div class="flex justify-around gap-2">
	<SearchUsingParam placeholder="Search..." />

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['id_kendaraan', 'ID Kendaraan'],
		['nama_kendaraan', 'Nama Ken<PERSON>aan'],
		['nomor_polisi', 'Nomor Polisi'],
		['pemilik', 'Pemilik'],
		['keterangan', 'Keterangan'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Actions'}
			<div class="flex items-center gap-2">
				<button class="btn btn-sm btn-outline btn-primary uppercase">
					<Icon icon="mdi:open-in-new" />
				</button>
				<button class="btn btn-sm btn-outline btn-primary uppercase">
					<Icon icon="mdi:pencil" />
				</button>
				<button class="btn btn-sm btn-outline btn-primary uppercase">
					<Icon icon="mdi:trash-can" />
				</button>
			</div>
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.list.length} />
</div>
