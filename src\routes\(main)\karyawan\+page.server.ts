import type { Actions, PageServerLoad } from './$types';
import { KaryawanSchema, type Karyawan } from '$lib/schema/general';

import { launch, retrieve } from '$lib/utils/fetch';
import { Effect } from 'effect';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';
import { FileFromSelf } from '$lib/schema/basic';

export const load: PageServerLoad = async ({ fetch, url }) => {
	const limit = url.searchParams.get('limit') ?? '10';
	const offset = url.searchParams.get('offset') ?? '1';
	const keyword = url.searchParams.get('keyword') ?? '';

	const getKaryawan = retrieve<Karyawan[]>(fetch, `/karyawan`);
	const list = await Effect.runPromise(getKaryawan);

	return { list: list.data, total_rows: list.total_rows };
};

export const actions: Actions = {
	'action:karyawan': async ({ request, fetch }) => {
		const data = await request.formData();

		const foto = data.get('foto') as File;
		const karyawan = JSON.parse(data.get('karyawan') as string) as Karyawan;
		const mode = data.get('mode') as 'add' | 'edit';

		const pickedAttributes: (keyof Karyawan)[] = [
			'id_karyawan',
			'nama',
			'alamat',
			'no_telp',
			'keterangan',
			'username',
			'password',
			'jabatan',
			'bengkel',
			'foto'
		];
		if (mode === 'edit') pickedAttributes.pop();

		const FormKaryawanSchema = KaryawanSchema.pick(...pickedAttributes);

		const decoded = decodeForm<typeof FormKaryawanSchema.Type, typeof FormKaryawanSchema.Encoded>(
			FormKaryawanSchema,
			{ ...karyawan, foto }
		);

		if ('error' in decoded) return fail(400, { errors: decoded.errors });

		data.delete('karyawan');
		data.delete('mode');
		data.append('id_bengkel', decoded.bengkel.id_bengkel);
		data.set('alamat', JSON.stringify(decoded.alamat));

		const action = launch(fetch, '/karyawan', {
			method: mode === 'add' ? 'POST' : 'PUT',
			headers: {
				Accept: '*/*',
				'Accept-Encoding': 'gzip, deflate, br',
				Connection: 'keep-alive'
			},
			body: data
		});

		const response = await Effect.runPromise(action);
		return response;
	},
	'delete:karyawan': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id') as string;

		const action = launch(fetch, `/karyawan/${id}`, {
			method: 'DELETE'
		});

		const response = await Effect.runPromise(action);
		return response;
	}
};
