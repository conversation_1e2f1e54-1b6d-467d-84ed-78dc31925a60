/* eslint-disable @typescript-eslint/no-empty-object-type */
import { Schema } from 'effect';
import { tipeCustomer } from './literal';
import { DurasiSchema, FileFromSelf, NoTelpSchema, PasswordSchema, UsernameSchema } from './basic';
import { KategoriSparepartSchema, SatuanSchema } from './sparepart';

export const AlamatSchema = Schema.Struct({
	jalan: Schema.NonEmptyString.annotations({
		message: () => 'Alamat Jalan Tidak Boleh Kosong'
	}),
	'rt/rw': Schema.String,
	kelurahan: Schema.String,
	kecamatan: Schema.String,
	kota: Schema.String,
	provinsi: Schema.String
});

export interface Alamat extends Schema.Schema.Type<typeof AlamatSchema> {}
export interface AlamatEncoded extends Schema.Schema.Encoded<typeof AlamatSchema> {}

export const _Alamat: Alamat = {
	jalan: '',
	'rt/rw': '',
	kel<PERSON>han: '',
	kecamatan: '',
	kota: '',
	provinsi: ''
};

export const JasaSchema = Schema.Struct({
	id_jasa: Schema.String,

	nama_jasa: Schema.String.annotations({ message: () => 'Nama Jasa Tidak Boleh Kosong' }),
	harga: Schema.Number,
	durasi_estimasi: DurasiSchema,
	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Jasa extends Schema.Schema.Type<typeof JasaSchema> {}
export interface JasaEncoded extends Schema.Schema.Encoded<typeof JasaSchema> {}

export const _Jasa: Jasa = {
	id_jasa: '',
	nama_jasa: '',
	harga: 0,
	durasi_estimasi: 0,
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const SparepartSchema = Schema.Struct({
	kode_sparepart: Schema.String,

	id_satuan: Schema.NullOr(SatuanSchema),
	id_kategori_sparepart: Schema.NullOr(KategoriSparepartSchema),

	nama_sparepart: Schema.NonEmptyString.annotations({
		message: () => 'Nama Sparepart Tidak Boleh Kosong'
	}),

	merk_motor: Schema.NullOr(Schema.String),
	jenis_motor: Schema.NullOr(Schema.String),

	stok_minimum: Schema.Number.pipe(
		Schema.greaterThan(0, { message: () => 'Stok Minimum Harus Lebih Dari 0' })
	),

	stok_barang: Schema.Number.pipe(
		Schema.greaterThan(0, { message: () => 'Stok Minimum Harus Lebih Dari 0' })
	),

	harga_beli: Schema.Number.pipe(
		Schema.greaterThan(0, { message: () => 'Harga Beli Harus Lebih Dari Rp 0' })
	),
	harga_jual: Schema.Number.pipe(
		Schema.greaterThan(0, { message: () => 'Harga Jual Harus Lebih Dari Rp 0' })
	),

	keterangan: Schema.NullOr(Schema.String),

	// link_gambar_sparepart: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
	keterangan_hapus: Schema.NullOr(Schema.String),

	id_karyawan: Schema.NonEmptyString,
	id_bengkel: Schema.NonEmptyString
});

export interface Sparepart extends Schema.Schema.Type<typeof SparepartSchema> {}
export interface SparepartEncoded extends Schema.Schema.Encoded<typeof SparepartSchema> {}

export const _Sparepart: Sparepart = {
	kode_sparepart: '',
	id_satuan: null,
	id_kategori_sparepart: null,
	nama_sparepart: '',
	merk_motor: null,
	jenis_motor: null,
	stok_minimum: 0,
	stok_barang: 0,
	harga_beli: 0,
	harga_jual: 0,
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null,
	keterangan_hapus: null,
	id_karyawan: '',
	id_bengkel: ''
};

export const PaketSchema = Schema.Struct({
	id_paket: Schema.String,

	nama_paket: Schema.NonEmptyString.annotations({ message: () => 'Nama Paket Tidak Boleh Kosong' }),
	harga: Schema.Number.pipe(
		Schema.greaterThan(0, { message: () => 'Harga Harus Lebih Dari Rp 0' })
	),
	durasi_estimasi: DurasiSchema,
	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.String,
	deleted_at: Schema.String,

	jasa_paket: Schema.Array(JasaSchema),
	sparepart_paket: Schema.Array(SparepartSchema)
});

export interface Paket extends Schema.Schema.Type<typeof PaketSchema> {}
export interface PaketEncoded extends Schema.Schema.Encoded<typeof PaketSchema> {}

export const _Paket: Paket = {
	id_paket: '',
	nama_paket: '',
	harga: 0,
	durasi_estimasi: 0,
	keterangan: null,
	created_at: '',
	updated_at: '',
	deleted_at: '',
	jasa_paket: [],
	sparepart_paket: []
};

// //////////////////////////////////

export const CustomerSchema = Schema.Struct({
	id_customer: Schema.String,

	nama: Schema.NonEmptyString.annotations({
		message: () => 'Nama Customer Tidak Boleh Kosong'
	}),
	alamat: AlamatSchema,
	no_telp: NoTelpSchema,

	username: UsernameSchema,
	password: PasswordSchema,

	tipe_customer: Schema.Literal(...tipeCustomer),
	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Customer extends Schema.Schema.Type<typeof CustomerSchema> {}
export interface CustomerEncoded extends Schema.Schema.Encoded<typeof CustomerSchema> {}

export const _Customer: Customer = {
	id_customer: '',
	nama: '',
	alamat: _Alamat,
	no_telp: '',
	username: '',
	password: '',
	tipe_customer: 'Individu',
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const CustomerLoginSchema = Schema.Struct({
	id_customer: Schema.String,

	last_login: Schema.String,
	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.String
});

export interface CustomerLogin extends Schema.Schema.Type<typeof CustomerLoginSchema> {}
export interface CustomerLoginEncoded extends Schema.Schema.Encoded<typeof CustomerLoginSchema> {}

export const _CustomerLogin: CustomerLogin = {
	id_customer: '',
	last_login: '',
	created_at: '',
	updated_at: null,
	deleted_at: ''
};

export const KendaraanSchema = Schema.mutable(
	Schema.Struct({
		id_kendaraan: Schema.String,

		nomor_polisi: Schema.String,
		pemilik: CustomerSchema,

		nama_kendaraan: Schema.String,
		cc_kendaraan: Schema.Number,
		tahun_kendaraan: Schema.Number,

		keterangan: Schema.NullOr(Schema.String),

		created_at: Schema.String,
		updated_at: Schema.NullOr(Schema.String),
		deleted_at: Schema.NullOr(Schema.String)
	})
);

export interface Kendaraan extends Schema.Schema.Type<typeof KendaraanSchema> {}
export interface KendaraanEncoded extends Schema.Schema.Encoded<typeof KendaraanSchema> {}

export const _Kendaraan: Kendaraan = {
	id_kendaraan: '',
	nomor_polisi: '',
	pemilik: _Customer,
	nama_kendaraan: '',
	cc_kendaraan: 0,
	tahun_kendaraan: 0,
	created_at: '',
	updated_at: null,
	deleted_at: null,
	keterangan: null
};

// ///////////////////////////////

export const BengkelSchema = Schema.Struct({
	id_bengkel: Schema.String,

	nama_bengkel: Schema.String,
	alamat: Schema.String,
	no_telp: Schema.String,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Bengkel extends Schema.Schema.Type<typeof BengkelSchema> {}
export interface BengkelEncoded extends Schema.Schema.Encoded<typeof BengkelSchema> {}

export const _Bengkel: Bengkel = {
	id_bengkel: '',
	nama_bengkel: '',
	alamat: '',
	no_telp: '',
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const MontirSchema = Schema.Struct({
	id_montir: Schema.String,
	id_bengkel: Schema.String,

	nama: Schema.String,
	alamat: Schema.String,
	no_telp: Schema.String,
	spesialisasi: Schema.String,
	status_montir: Schema.String,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Montir extends Schema.Schema.Type<typeof MontirSchema> {}
export interface MontirEncoded extends Schema.Schema.Encoded<typeof MontirSchema> {}

export const _Montir: Montir = {
	id_montir: '',
	id_bengkel: '',
	nama: '',
	alamat: '',
	no_telp: '',
	spesialisasi: '',
	status_montir: '',
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const AfiliasiSchema = Schema.Struct({
	id_bengkel: Schema.String,
	id_montir: Schema.Number,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Afiliasi extends Schema.Schema.Type<typeof AfiliasiSchema> {}
export interface AfiliasiEncoded extends Schema.Schema.Encoded<typeof AfiliasiSchema> {}

export const _Afiliasi: Afiliasi = {
	id_bengkel: '',
	id_montir: 0,
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const KaryawanSchema = Schema.mutable(
	Schema.Struct({
		bengkel: BengkelSchema,
		id_karyawan: Schema.String,

		nama: Schema.NonEmptyString.annotations({
			message: () => 'Nama Karyawan Tidak Boleh Kosong'
		}),

		alamat: AlamatSchema,
		no_telp: NoTelpSchema,

		jabatan: Schema.NonEmptyString.annotations({
			message: () => 'Jabatan Tidak Boleh Kosong'
		}),

		status: Schema.String,
		keterangan: Schema.String,

		username: UsernameSchema,
		password: PasswordSchema,

		foto: Schema.NullOr(FileFromSelf),

		created_at: Schema.String,
		updated_at: Schema.String,
		deleted_at: Schema.NullOr(Schema.String)
	})
);

export interface Karyawan extends Schema.Schema.Type<typeof KaryawanSchema> {}
export interface KaryawanEncoded extends Schema.Schema.Encoded<typeof KaryawanSchema> {}

export const _Karyawan: Karyawan = {
	bengkel: _Bengkel,
	id_karyawan: '',
	nama: '',
	alamat: _Alamat,
	no_telp: '',
	jabatan: '',
	status: '',
	keterangan: '',
	username: '',
	password: '',
	foto: null,
	created_at: '',
	updated_at: '',
	deleted_at: null
};

export const KaryawanLoginSchema = Schema.mutable(
	Schema.Struct({
		id_karyawan: Schema.String,

		last_login: Schema.String,
		created_at: Schema.String,
		updated_at: Schema.String,
		deleted_at: Schema.NullOr(Schema.String)
	})
);

export interface KaryawanLogin extends Schema.Schema.Type<typeof KaryawanLoginSchema> {}
export interface KaryawanLoginEncoded extends Schema.Schema.Encoded<typeof KaryawanLoginSchema> {}

export const _KaryawanLogin: KaryawanLogin = {
	id_karyawan: '',
	last_login: '',
	created_at: '',
	updated_at: '',
	deleted_at: null
};

// ///////////////////////////

export const UserManagementSchema = Schema.mutable(
	Schema.Struct({
		id_user_management: Schema.String,

		nama_user_management: Schema.String,

		keterangan: Schema.NullOr(Schema.String),

		created_at: Schema.String,
		updated_at: Schema.String,
		deleted_at: Schema.NullOr(Schema.String)
	})
);

export interface UserManagement extends Schema.Schema.Type<typeof UserManagementSchema> {}
export interface UserManagementEncoded extends Schema.Schema.Encoded<typeof UserManagementSchema> {}

export const _UserManagement: UserManagement = {
	id_user_management: '',
	nama_user_management: '',
	keterangan: null,
	created_at: '',
	updated_at: '',
	deleted_at: null
};

export const DetailUserManagementSchema = Schema.Struct({
	id_detail_user_management: Schema.String,
	id_user_management: Schema.String,
	id_karyawan: Schema.String,

	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.String,
	deleted_at: Schema.NullOr(Schema.String)
});

export interface DetailUserManagement
	extends Schema.Schema.Type<typeof DetailUserManagementSchema> {}
export interface DetailUserManagementEncoded
	extends Schema.Schema.Encoded<typeof DetailUserManagementSchema> {}

export const _DetailUserManagement: DetailUserManagement = {
	id_detail_user_management: '',
	id_user_management: '',
	id_karyawan: '',
	keterangan: null,
	created_at: '',
	updated_at: '',
	deleted_at: null
};

// ///////////

export const StatusSchema = Schema.Struct({
	id_status: Schema.String,

	nama_status: Schema.String,
	keterangan: Schema.NullOr(Schema.String)
});

export interface Status extends Schema.Schema.Type<typeof StatusSchema> {}
export interface StatusEncoded extends Schema.Schema.Encoded<typeof StatusSchema> {}

export const _Status: Status = {
	id_status: '',
	nama_status: '',
	keterangan: null
};

// ///////////
