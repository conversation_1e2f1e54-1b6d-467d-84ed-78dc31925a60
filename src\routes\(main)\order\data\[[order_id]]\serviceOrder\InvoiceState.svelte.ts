import { _Order, type Invoice, type Order } from '$lib/schema/order';
import { getContext, setContext } from 'svelte';

export interface InvoiceState {
	order: Order;
}

export default class InvoiceStateClass {
	invoice = $state<Partial<Invoice>>({});
	order = $state<Order>(_Order);

	constructor(_invoice: Invoice) {
		if (!_invoice.nomor_invoice) this.order = _Order;
		else this.order = _invoice.order;
	}
}

const INVOICE_STATE_KEY = Symbol('@@invoice-state@@');

export function setInvoiceState(_invoice: Invoice): void {
	setContext(INVOICE_STATE_KEY, new InvoiceStateClass(_invoice));
}

export function getInvoiceState(): InvoiceState {
	return getContext<InvoiceState>(INVOICE_STATE_KEY);
}
