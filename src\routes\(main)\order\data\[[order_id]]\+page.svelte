<script lang="ts">
	import Icon from '@iconify/svelte';
	import {
		getServiceOrderState,
		setServiceOrderState
	} from './serviceOrder/ServiceOrderState.svelte.js';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte.js';
	import ServiceOrderTable from './components/ServiceOrderTable.svelte';

	import logoCombined from '$lib/images/logo-combined.webp';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import { getInvoiceState, setInvoiceState } from './serviceOrder/InvoiceState.svelte.js';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte.js';

	const { data } = $props();

	let self = data.invoice.order;

	setServiceOrderState();
	const serviceOrder = getServiceOrderState();

	let invoiceState = $state(getInvoiceState());
	setInvoiceState(data.invoice);
	$effect(() => {
		invoiceState = getInvoiceState();
	});

	setValidationErrorState();
	$inspect(invoiceState.order);
</script>

<main class="flex min-h-full flex-col gap-2">
	<section class="h-fit shrink-0 overflow-auto">
		<div class="flex h-fit shrink-0 gap-2 border-b-2 border-b-gray-300">
			{#if invoiceState}
				<aside class="flex w-5/12 shrink-0 grow flex-col gap-2 p-4 text-xs">
					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit shrink-0 font-semibold">Nomor Polisi</p>
						<p class="w-2">:</p>
						<div class="flex grow items-center gap-4">
							<FormField
								noLabel
								name="nomor_polisi"
								placeholder="Nomor Polisi"
								type="text"
								bind:value={invoiceState.order.kendaraan.nomor_polisi}
							></FormField>

							<div class="text-primary flex items-center gap-2">
								<p class="w-3/12 min-w-fit font-semibold">CC</p>
								<p class="w-2">:</p>
								<div class="grow">
									<FormField
										noLabel
										name="cc_kendaraan"
										placeholder="CC"
										type="number"
										bind:value={invoiceState.order.kendaraan.cc_kendaraan}
										step="50"
									></FormField>
								</div>
							</div>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Kendaraan</p>
						<p class="w-2">:</p>
						<div class="grow">
							<input type="text" name="kendaraan" id="kendaraan" class="input input-sm w-full" />
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Pemilik</p>
						<p class="w-2">:</p>
						<div class="grow">
							<input type="text" name="pemilik" id="pemilik" class="input input-sm w-full" />
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-2/12 min-w-fit font-semibold">Pengantar</p>
						<p class="w-2">:</p>
						<div class="grow">
							<input type="text" name="pengantar" id="pengantar" class="input input-sm w-full" />
						</div>
					</div>
				</aside>
			{/if}

			<aside
				class="flex w-2/12 shrink flex-col items-center justify-center gap-1 border-x-2 border-x-gray-300 px-4 text-center"
			>
				<img src={logoCombined} alt="Logo Text" class="w-3/4" />
			</aside>

			<aside class="w-5/12 shrink-0 grow p-4 text-xs">
				<div class="mb-2 grid grid-cols-2 items-center gap-2">
					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Tanggal Order</p>
						<p class="w-2">:</p>
						<div class="grow"><p>{self?.created_at.split('T')[0]}</p></div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Jam</p>
						<p class="w-2">:</p>
						<div class="grow"><p>{self?.created_at.split('T')[1]}</p></div>
					</div>
				</div>

				<div class="mb-2 grid grid-cols-2 items-center gap-2">
					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Nomor Order</p>
						<p class="w-2">:</p>
						<div class="grow">
							<p>{self?.nomor_order}</p>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Nomor Invoice</p>
						<p class="w-2">:</p>
						<div class="grow">
							<p>{data.invoice?.nomor_invoice}</p>
						</div>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Nomor HP</p>
					<p class="w-2">:</p>
					<div class="grow">
						<input
							type="text"
							name="nomor_telepon"
							id="nomor_telepon"
							class="input input-sm w-full"
						/>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Jenis Layanan</p>
					<p class="w-2">:</p>
					<div class="grow">
						<select name="jenis_layanan" id="jenis_layanan" class="select select-sm">
							<option value="" disabled>-- Pilih Jenis Layanan --</option>
							<option value="bengkel">Di Bengkel</option>
							<option value="tempat">Di Tempat</option>
							<option value="rumah">Di Rumah </option>
						</select>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Alamat</p>
					<p class="w-2">:</p>
					<div class="grow">
						<input type="text" name="alamat" id="alamat" class="input input-sm w-full" />
					</div>
				</div>
			</aside>
		</div>
	</section>

	<section
		class="invoice-background flex flex-col gap-4 border-2 border-gray-100"
		style="background-image: url({logoCombined}), url({logoCombined}), url({logoCombined});"
	>
		<section class="grow overflow-auto">
			<!-- <ServiceOrderTable list={{ ...data.list, montir: data.listMontir }} /> -->
		</section>

		<section class="mb-2 flex justify-end">
			<div class="w-1/3 text-sm">
				<div class="mb-3 border-y-2 border-y-gray-300 py-3">
					<div class="text-primary mb-2 flex items-center gap-2">
						<p class="w-3/12 min-w-fit font-semibold">Subtotal</p>
						<p class="w-2">:</p>
						<p>{rupiah(serviceOrder.subtotal)}</p>
					</div>

					<div class="text-primary mb-2 flex items-center gap-2">
						<p class="w-3/12 min-w-fit shrink-0 font-semibold">Diskon</p>
						<p class="w-2">:</p>
						<div class="flex shrink-0 items-center gap-2">
							<input type="text" class="input input-xs w-20" placeholder="Persen" />
							<p>%</p>
							<input
								type="text"
								class="input input-xs"
								placeholder="Diskon"
								bind:value={serviceOrder.diskon}
							/>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-3/12 min-w-fit font-semibold">PPN</p>
						<p class="w-2">:</p>
						<div class="flex shrink-0 items-center gap-2">
							<input
								type="text"
								class="input input-xs w-20"
								placeholder="Persen"
								bind:value={serviceOrder.ppn}
							/>
							<p>%</p>
						</div>
					</div>
				</div>

				<div class="text-primary mb-4 flex items-center gap-2 text-base">
					<p class="w-3/12 min-w-fit font-semibold">Total</p>
					<p class="w-2">:</p>
					<p>{rupiah(serviceOrder.total)}</p>
				</div>
			</div>
		</section>
	</section>

	<footer class="flex h-1/12 items-center justify-end gap-2 p-4">
		<button class="btn btn-primary">
			<Icon icon="ri:download-2-fill" /> Simpan
		</button>
	</footer>
</main>

<style>
	.invoice-background {
		background-size: 150px;
		background-color: rgba(255, 255, 255, 0.95);
		background-blend-mode: lighten;
		background-position: 0 0;
		background-repeat: repeat;
	}
</style>
