import type { Montir, Order } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const montirId = params.id;

	const riwayat: Order[] = [
		{
			nomor_order: 'AB-123',
			created_at: '2025-01-01',
			customer: {
				nama: '<PERSON>',
				alamat: 'Jl. ABC No. 123',
				no_telp: '081234567890',
				tipe_customer: 'perorangan',
				id: '1'
			},
			status: 'selesai',
			nomor_invoice: 'AB-123',
			id_montir: '1',
			id_bengkel: '1',
			id_karyawan: '1',
			id_customer: '1',
			id_kendaraan: '1',
			id_sparepart: '1',
			id_paket: '1',
			kode_jasa: '1',
			pengantar: '<PERSON>',
			jenis_layanan: 'Service',
			alamat: 'Jl. ABC No. 123',
			status_order: 'selesai',
			jenis_service: 'Service Rutin',
			kuantitas: 1,
			keluhan: 'Kendaraan tidak bisa jalan',
			diagnosis: 'Ban kempes',
			diskon: 0,
			pajak: 0,
			metode_pembayaran: 'Cash',
			keterangan: 'Keterangan',
			updated_at: '2025-01-01',
			deleted_at: null
		}
	];

	const montir: Montir = {
		id_montir: '1',
		nama: 'Saul Goodman',
		alamat: 'Jl. ABC No. 123',
		no_telp: '081234567890',
		spesialisasi: 'Service',
		status_montir: 'aktif',
		keterangan: null,
		created_at: '2025-01-01',
		updated_at: null,
		deleted_at: null,
		id_bengkel: '1'
	};

	return { riwayat, montir, montirId };
};
