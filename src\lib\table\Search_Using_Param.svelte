<script lang="ts">
	import { debounce, mutateQueryParams } from '$lib/scripts/utils';
	import Icon from '@iconify/svelte';

	interface IProps {
		placeholder: string;
		autofocus?: boolean;
	}
	const { placeholder = '', autofocus = true }: IProps = $props();

	let keyword = $state('');

	const updateKeyword = debounce(() => {
		mutateQueryParams('keyword', () => keyword);
	}, 500);

	$effect(() => updateKeyword(keyword));
</script>

<label class="input input-sm w-full">
	<Icon icon="majesticons:search-line" />
	<input
		type="search"
		name="keyword"
		id="keyword"
		{placeholder}
		bind:value={keyword}
		autocomplete="off"
	/>
</label>
