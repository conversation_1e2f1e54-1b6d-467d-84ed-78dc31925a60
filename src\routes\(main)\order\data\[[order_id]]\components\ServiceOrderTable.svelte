<script lang="ts">
	import type { <PERSON><PERSON>, Montir as MontirType, Paket, Sparepart } from '$lib/schema/general';
	import { getServiceOrderState } from '../serviceOrder/ServiceOrderState.svelte';

	import Table from '$lib/table/Table.svelte';

	import JenisService from './table/JenisService.svelte';
	import Harga from './table/Harga.svelte';
	import Qty from './table/Qty.svelte';
	import HargaSatuan from './table/HargaSatuan.svelte';

	import Montir from './table/Montir.svelte';

	const serviceOrder = getServiceOrderState();

	interface IProps {
		list: {
			paket: Paket[];
			jasa: Jasa[];
			sparepart: Sparepart[];
			montir: MontirType[];
		};
	}

	const { list }: IProps = $props();
</script>

<Table
	table_header={[
		['numbering', 'No.'],
		['custom', 'Jenis Service'],
		['custom', 'Harga Satuan'],
		['custom', 'QTY'],
		['custom', 'Nomor Montir'],
		['custom', 'Harga']
	]}
	table_data={serviceOrder.orders}
>
	{#snippet custom({ header, body, index })}
		{#if header === 'Jenis Service'}
			<JenisService {body} {list} />
		{:else if header === 'QTY'}
			<Qty {body} {index} />
		{:else if header === 'Harga Satuan'}
			<HargaSatuan {body} />
		{:else if header === 'Nomor Montir'}
			<Montir {body} list={list.montir} {index} />
		{:else if header === 'Harga'}
			<Harga {body} />
		{/if}
	{/snippet}
</Table>
