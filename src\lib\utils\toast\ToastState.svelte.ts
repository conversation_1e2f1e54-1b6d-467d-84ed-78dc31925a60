import { getContext, onDestroy, setContext } from 'svelte';

export type Toast = {
	message: string;

	type?: 'success' | 'error' | 'warning' | 'info';
	durationMs?: number;

	id?: string;
};

export interface ToastState {
	toasts: Toast[];
	add(toast: Toast): void;
	remove(toast: Toast): void;
	toastToTimeout: Map<string, number>;
}

export default class ToastStateClass implements ToastState {
	#toasts = $state<Toast[]>([]);
	toastToTimeout: Map<string, number> = $state(new Map<string, number>());

	constructor() {
		onDestroy(() => {
			for (const timeout of this.toastToTimeout.values()) {
				clearTimeout(timeout);
			}
		});
	}

	get toasts() {
		return this.#toasts;
	}

	add(toast: Toast) {
		const id = toast.id ?? Math.random().toString(36).substring(2, 9);
		const durationMs = toast.durationMs ?? 3000;
		const type = toast.type ?? 'info';

		toast = { ...toast, id, type, durationMs };

		this.#toasts.push(toast);

		this.toastToTimeout.set(
			id,
			setTimeout(() => {
				this.remove(toast);
			}, toast.durationMs)
		);
	}

	remove(toast: Toast) {
		if (!toast.id) {
			alert('Toast ID is undefined');
			return;
		}

		const timeout = this.toastToTimeout.get(toast.id);
		if (timeout) {
			this.toastToTimeout.delete(toast.id);
		}

		this.#toasts = this.#toasts.filter((t) => t.id !== toast.id);
	}
}

const TOAST_STATE_KEY = Symbol('toast-state');

export function setToastState(): void {
	setContext(TOAST_STATE_KEY, new ToastStateClass());
}

export function getToastState(): ToastState {
	return getContext<ToastState>(TOAST_STATE_KEY);
}
