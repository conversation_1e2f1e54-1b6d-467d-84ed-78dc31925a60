<script lang="ts">
	import { enhance } from '$app/forms';

	import Currency from '$lib/inputs/Currency.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} jasa ini?`,
				loader: 'action:jasa'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();
	let currencyReadonly = $derived(detailState.mode === 'view');

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Jasa</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<label for="id_jasa" class="floating-label mb-4" class:hidden={detailState.mode === 'add'}>
			<span>ID Jasa</span>
			<input
				type="text"
				name="id_jasa"
				id="id_jasa"
				class="input w-full"
				bind:value={detailState.body.id_jasa}
				placeholder="ID Jasa"
				disabled
			/>
		</label>

		<label for="nama_jasa" class="floating-label">
			<span>Nama Jasa</span>
			<input
				type="text"
				name="nama_jasa"
				id="nama_jasa"
				class="input w-full"
				bind:value={detailState.body.nama_jasa}
				placeholder="Nama Jasa"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nama_jasa" />
		</label>

		<br />

		<label for="harga" class="floating-label">
			<span>Harga</span>
			<Currency
				name="harga"
				id="harga"
				class="w-full {detailState.mode === 'view' ? 'border-dashed' : ''}"
				bind:value={detailState.body.harga}
				bind:readonly={currencyReadonly}
			/>
			<ValidationError name="harga" />
		</label>

		<br />

		<label for="durasi_estimasi" class="floating-label">
			<span>Durasi Estimasi (Menit)</span>
			<input
				type="number"
				name="durasi_estimasi"
				id="durasi_estimasi"
				class="input w-full"
				bind:value={detailState.body.durasi_estimasi}
				placeholder="Durasi Estimasi (Menit)"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="durasi_estimasi" />
		</label>

		<br />

		<label for="keterangan" class="floating-label">
			<span>Keterangan</span>
			<textarea
				name="keterangan"
				id="keterangan"
				class="textarea w-full"
				bind:value={detailState.body.keterangan}
				placeholder="Keterangan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			></textarea>
			<ValidationError name="keterangan" />
		</label>

		<br />

		<form
			action="?/action:jasa"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="jasa" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:jasa">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>
