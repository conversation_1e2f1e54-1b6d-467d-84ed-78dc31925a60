import { Schema } from 'effect';
import {
	AlamatSchema,
	BengkelSchema,
	CustomerSchema,
	KaryawanSchema,
	JasaSchema,
	SparepartSchema,
	KendaraanSchema,
	_<PERSON><PERSON><PERSON>,
	_<PERSON><PERSON><PERSON>,
	_Customer,
	_<PERSON><PERSON><PERSON>,
	_Kendar<PERSON>
} from './general';
import { statusTransaksi } from './literal';

export const OrderSchema = Schema.Struct({
	nomor_order: Schema.String,

	bengkel: BengkelSchema,
	karyawan: KaryawanSchema,
	customer: CustomerSchema,
	kendaraan: KendaraanSchema,

	pengantar: Schema.String,
	jenis_layanan: Schema.String,
	alamat: AlamatSchema,

	status: Schema.Literal('antrian', 'dikerjakan', 'selesai', 'void'),

	keluhan: Schema.String,
	diagnosis: Schema.String,

	diskon: Schema.NullOr(Schema.Number),
	pajak: Schema.Number,
	metode_pembayaran: Schema.NullOr(Schema.String),
	keterangan: Schema.NullOr(Schema.String),

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface Order extends Schema.Schema.Type<typeof OrderSchema> {}
export interface OrderEncoded extends Schema.Schema.Encoded<typeof OrderSchema> {}

export const _Order: Order = {
	nomor_order: '',
	bengkel: _Bengkel,
	karyawan: _Karyawan,
	customer: _Customer,
	kendaraan: _Kendaraan,
	pengantar: '',
	jenis_layanan: '',
	alamat: _Alamat,
	status: 'antrian',
	keluhan: '',
	diagnosis: '',
	diskon: null,
	pajak: 0,
	metode_pembayaran: null,
	keterangan: null,
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const OrderSparepart = Schema.Struct({
	id_order_sparepart: Schema.Number,
	nomor_order: Schema.String,
	id_montir: Schema.String,
	kode_sparepart: Schema.String,

	nama_sparepart: Schema.String,
	harga: Schema.Number,
	kuantitas: Schema.Number,

	keterangan: Schema.String,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface OrderSparepart extends Schema.Schema.Type<typeof OrderSparepart> {}
export interface OrderSparepartEncoded extends Schema.Schema.Encoded<typeof OrderSparepart> {}

export const _OrderSparepart: OrderSparepart = {
	id_order_sparepart: 0,
	nomor_order: '',
	id_montir: '',
	kode_sparepart: '',
	nama_sparepart: '',
	harga: 0,
	kuantitas: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const OrderJasa = Schema.Struct({
	id_order_jasa: Schema.Number,
	nomor_order: Schema.String,
	id_montir: Schema.String,
	id_jasa: Schema.String,

	nama_jasa: Schema.String,
	kuantitas: Schema.Number,
	harga: Schema.Number,

	keterangan: Schema.String,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface OrderJasa extends Schema.Schema.Type<typeof OrderJasa> {}
export interface OrderJasaEncoded extends Schema.Schema.Encoded<typeof OrderJasa> {}

export const _OrderJasa: OrderJasa = {
	id_order_jasa: 0,
	nomor_order: '',
	id_montir: '',
	id_jasa: '',
	nama_jasa: '',
	kuantitas: 0,
	harga: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

export const OrderPaket = Schema.Struct({
	id_order_paket: Schema.Number,
	nomor_order: Schema.String,
	id_montir: Schema.String,
	id_paket: Schema.String,

	nama_paket: Schema.String,
	jasa_paket: Schema.Array(JasaSchema),
	sparepart_paket: Schema.Array(SparepartSchema),

	kuantitas: Schema.Number,
	harga: Schema.Number,

	keterangan: Schema.String,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String)
});

export interface OrderPaket extends Schema.Schema.Type<typeof OrderPaket> {}
export interface OrderPaketEncoded extends Schema.Schema.Encoded<typeof OrderPaket> {}

export const _OrderPaket: OrderPaket = {
	id_order_paket: 0,
	nomor_order: '',
	id_montir: '',
	id_paket: '',
	nama_paket: '',
	jasa_paket: [],
	sparepart_paket: [],
	kuantitas: 0,
	harga: 0,
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null
};

//

export const InvoiceSchema = Schema.Struct({
	nomor_invoice: Schema.String,

	order: OrderSchema,
	karyawan: KaryawanSchema,

	subtotal: Schema.Number,
	total: Schema.Number,

	status_transaksi: Schema.Literal(...statusTransaksi),
	keterangan: Schema.String,

	created_at: Schema.String,
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),

	void_status: Schema.String,
	keterangan_void: Schema.String
});

export interface Invoice extends Schema.Schema.Type<typeof InvoiceSchema> {}
export interface InvoiceEncoded extends Schema.Schema.Encoded<typeof InvoiceSchema> {}

export const _Invoice: Invoice = {
	nomor_invoice: '',
	order: _Order,
	karyawan: _Karyawan,
	subtotal: 0,
	total: 0,
	status_transaksi: 'Belum Dibayar',
	keterangan: '',
	created_at: '',
	updated_at: null,
	deleted_at: null,
	void_status: '',
	keterangan_void: ''
};
