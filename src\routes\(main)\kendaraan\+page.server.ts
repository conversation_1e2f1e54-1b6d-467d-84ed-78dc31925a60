import type { Kendaraan } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	const list: Kendaraan[] = [
		{
			id_kendaraan: 'AC-123',
			nomor_polisi: 'B 1234 ABC',
			pemilik: '1',
			nama_kendaraan: 'APV',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan: 'APV Arena 2003'
		},
		{
			id_kendaraan: 'AB-123',
			nomor_polisi: 'L 4243 AAF',
			pemilik: '1',
			nama_kendaraan: 'Avanza',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan: 'Avanza 1.5G CVT'
		},
		{
			id_kendaraan: 'DE-123',
			nomor_polisi: 'M 4 KAN',
			pemilik: '1',
			nama_kendaraan: 'Agya',
			created_at: '2025-01-01',
			updated_at: null,
			deleted_at: null,
			keterangan: 'Agya 1.2G GR Sport'
		}
	];

	return { list };
};
