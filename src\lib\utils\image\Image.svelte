<script lang="ts">
	import Loading from '../Loading.svelte';
	import { browser } from '$app/environment';
	import { getDrawerState, setDrawerState } from '$lib/drawer/runes.svelte';
	import Drawer from '$lib/drawer/Drawer.svelte';
	import { cn } from '$lib/utils';

	interface IProps {
		path: string;
		title: string;
		class?: string;
	}

	const { path, title, class: className }: IProps = $props();

	let promise = $state<Promise<any>>();
	$effect(() => {
		if (browser)
			promise = fetch(window.location.origin + `/api/image/${path}`).then((r) => r.json());
		else promise = undefined;

		return () => {
			promise = undefined;
		};
	});

	let image = $state<HTMLImageElement | undefined>(undefined);
	let src = $state<string | undefined>(undefined);

	setDrawerState();
	const drawer = getDrawerState();

	export function changeSource(_src: string | undefined | false) {
		if (!image || !_src) return;
		src = _src;
		image.src = src;
	}
</script>

{#if promise}
	{#await promise}
		<div class="grid aspect-video w-full place-items-center border bg-stone-100">
			<Loading notext></Loading>
		</div>
	{:then response}
		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<div
			class={cn(
				'flex aspect-video w-full flex-col content-center items-center justify-center border bg-stone-50 hover:cursor-pointer hover:brightness-90',
				className ?? ''
			)}
			onclick={() => (drawer.opened = true)}
		>
			{#if response && response.status === 200}
				<img
					src={response.src}
					alt="{title} not loaded properly"
					class="block aspect-video w-full"
					bind:this={image}
				/>
			{:else}
				{@render not_found_illustration()}
			{/if}
		</div>
	{:catch}
		<div
			class="flex aspect-video w-full flex-col content-center items-center justify-center border bg-stone-50"
		>
			{@render not_found_illustration()}
		</div>
	{/await}
{:else}
	<div class="text-main grid aspect-video w-full place-items-center border bg-stone-100">
		<Loading notext></Loading>
	</div>
{/if}

<Drawer>
	{#snippet header()}
		<h2>{title}</h2>
	{/snippet}
	<div>
		{#if image}
			<img src={image.src} alt={title} class="h-[90vh] w-[50vw]" />
		{/if}
	</div>
</Drawer>

{#snippet not_found_illustration()}
	<img
		src=""
		alt="{title} not loaded properly"
		class="block aspect-video w-full"
		bind:this={image}
		class:hidden={image && !src}
	/>

	{#if image && !src}
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
			><g
				fill="none"
				stroke="currentColor"
				stroke-linecap="round"
				stroke-linejoin="round"
				stroke-width="1.5"
				color="currentColor"
				><circle cx="16.5" cy="7.5" r="1.5" /><path
					d="M2 14.135q1.001-.135 2.016-.132c2.856-.056 5.642.77 7.86 2.331c2.06 1.448 3.505 3.44 4.124 5.666m-2.5-4.5c1-1 1.677-1.223 2.5-1.5"
				/><path
					d="M20 20.213C18.601 21.5 16.363 21.5 12 21.5c-4.478 0-6.718 0-8.109-1.391S2.5 16.479 2.5 12c0-4.363 0-6.601 1.287-8M20 16c.543 0 1.048.294 1.397.564c.103-1.195.103-2.681.103-4.564c0-4.478 0-6.718-1.391-8.109S16.479 2.5 12 2.5c-2.41 0-4.17 0-5.5.217M2 2l20 20"
				/></g
			></svg
		>
		<p class="text-xs">{path}</p>
		<p class="text-xs text-stone-400">{title}</p>
	{/if}
{/snippet}
