<script lang="ts" generics="T extends string | number | Record<string, any>">
	import { cn } from '..';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';
	import type { HTMLInputTypeAttribute } from 'svelte/elements';
	import Select from '$lib/inputs/Select.svelte';
	import type { Snippet } from 'svelte';

	interface IProps {
		name: string;
		id?: string;

		type: HTMLInputTypeAttribute | 'textarea' | 'select';
		value: T;
		class?: string;

		mode?: 'add' | 'view' | 'edit';

		noLabel: boolean;
		label?: string;
		placeholder?: string;

		select_options?: Snippet;
		matcher?: keyof T;
		list?: T[];

		validation?: boolean;
	}

	let {
		name,
		id = name,

		noLabel = false,
		label,
		placeholder = label,

		type,
		value = $bindable(),

		mode = $bindable('add'),

		class: className,

		select_options,
		matcher,
		list,

		validation = true,

		...restProps
	}: IProps = $props();
</script>

<label for={name} class=" {noLabel ? '' : 'floating-label'}">
	{#if noLabel}
		<span class="sr-only">{label}</span>
	{/if}

	{#if type === 'textarea'}
		<textarea
			{name}
			{id}
			{placeholder}
			readonly={mode === 'view'}
			class={cn('textarea w-full', className ?? '')}
			class:border-dashed={mode === 'view'}
			bind:value
			{...restProps}
		></textarea>
	{:else if type === 'select'}
		{#key value}
			<Select {name} {id} bind:value {matcher} list={list ?? []}>
				{@render select_options?.()}
			</Select>
		{/key}
	{:else}
		<input
			{type}
			{name}
			{id}
			{placeholder}
			readonly={mode === 'view'}
			class={cn('input w-full', className ?? '')}
			class:border-dashed={mode === 'view'}
			bind:value
			{...restProps}
		/>
	{/if}

	{#if validation}
		<ValidationError {name} />
	{/if}
</label>
