<script lang="ts">
	import { enhance } from '$app/forms';

	import Currency from '$lib/inputs/Currency.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import ValidationError from '$lib/utils/validation/ValidationError.svelte';
	import Icon from '@iconify/svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { type Jasa, type Sparepart } from '$lib/schema/general';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} paket ini?`,
				loader: 'action:paket'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();
	let currencyReadonly = $derived(detailState.mode === 'view');

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
			<h3 class="text-lg font-bold">Detail Paket</h3>
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-soft"
					onclick={() => (validationErrorState.errors = {})}>✕</button
				>
			</form>
		</div>

		<label for="id_paket" class="floating-label mb-4" class:hidden={detailState.mode === 'add'}>
			<span>ID Paket</span>
			<input
				type="text"
				name="id_paket"
				id="id_paket"
				class="input w-full"
				bind:value={detailState.body.id_paket}
				placeholder="ID Paket"
				disabled
			/>
		</label>

		<label for="nama_paket" class="floating-label">
			<span>Nama paket</span>
			<input
				type="text"
				name="nama_paket"
				id="nama_paket"
				class="input w-full"
				bind:value={detailState.body.nama_paket}
				placeholder="Nama paket"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="nama_paket" />
		</label>

		<br />

		<p class="mb-2 text-xs font-extralight">
			Total Harga Item Dipilih : <span class="font-normal"
				>{rupiah(detailState.totalHargaItem)}</span
			>
		</p>
		<label for="harga" class="floating-label">
			<span>Harga</span>
			<Currency
				name="harga"
				id="harga"
				class="w-full {detailState.mode === 'view' ? 'border-dashed' : ''}"
				bind:value={detailState.body.harga}
				bind:readonly={currencyReadonly}
			/>
			<ValidationError name="harga" />
		</label>

		<br />

		<label for="durasi_estimasi" class="floating-label">
			<span>Durasi Estimasi (Menit)</span>
			<input
				type="number"
				name="durasi_estimasi"
				id="durasi_estimasi"
				class="input w-full"
				bind:value={detailState.body.durasi_estimasi}
				placeholder="Durasi Estimasi (Menit)"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			/>
			<ValidationError name="durasi_estimasi" />
		</label>

		<br />

		<label for="keterangan" class="floating-label">
			<span>Keterangan</span>
			<textarea
				name="keterangan"
				id="keterangan"
				class="textarea w-full"
				bind:value={detailState.body.keterangan}
				placeholder="Keterangan"
				readonly={detailState.mode === 'view'}
				class:border-dashed={detailState.mode === 'view'}
			></textarea>
			<ValidationError name="keterangan" />
		</label>

		<div class="divider"></div>

		<div class="flex items-center justify-between">
			<h2>Daftar Item</h2>

			<div class="flex items-center gap-2" class:hidden={detailState.mode === 'view'}>
				<button
					class="btn btn-secondary btn-sm w-fit"
					type="button"
					onclick={() => utilityModalState.modal?.showModal()}
				>
					<Icon icon="mdi:wrench-clock-outline" /> Tambah Item Jasa
				</button>

				<button class="btn btn-secondary btn-sm w-fit">
					<Icon icon="mdi:car-battery" /> Tambah Item Sparepart
				</button>
			</div>
		</div>

		<ValidationError name="item" />

		<br />

		<div class="max-h-40 overflow-auto">
			<p class="mb-2 text-xs font-bold" class:hidden={detailState.jasaPaket.length === 0}>
				Jasa yang Dipilih :
			</p>
			{#each detailState.jasaPaket as jasa, i (jasa.id_jasa)}
				<div class="mb-1 flex items-center justify-between gap-2 px-2 text-xs">
					<div class="w-4 shrink-0 tabular-nums">{i + 1}.</div>
					<div class="grow text-left">
						{jasa.nama_jasa} <span class="font-light"> @ {rupiah(jasa.harga)}</span>
					</div>
					<button
						class="btn btn-xs btn-soft btn-error"
						class:hidden={detailState.mode === 'view'}
						onclick={() => detailState.jasaPaket.splice(i, 1)}
					>
						<Icon icon="mdi:trash-can" />
					</button>
				</div>
			{/each}
		</div>

		<br />

		<form
			action="?/action:paket"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					detailState.modal?.close();
				})}
		>
			<input type="hidden" name="paket" value={JSON.stringify(detailState.body)} />
			<input type="hidden" name="jasa_paket" value={JSON.stringify(detailState.jasaPaket)} />
			<input
				type="hidden"
				name="sparepart_paket"
				value={JSON.stringify(detailState.sparepartPaket)}
			/>
			<input type="hidden" name="mode" value={detailState.mode} />

			<button
				type="button"
				class="btn btn-primary w-full"
				onclick={(e) => confirmation(e)}
				class:hidden={detailState.mode === 'view'}
			>
				<ConfirmLoader name="action:paket">Simpan</ConfirmLoader>
			</button>
		</form>
	</div>
</dialog>

<UtilityModal url="/jasa">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Jasa</h2>
	{/snippet}

	{#snippet item({ item }: { item: Jasa })}
		{@const alreadySelected = detailState.jasaPaket.find((jasa) => jasa.id_jasa === item.id_jasa)}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start {alreadySelected
				? 'btn-disabled'
				: ''}"
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Jasa sudah dipilih',
						type: 'error'
					});
					return;
				}
				detailState.jasaPaket.push(item);
				toastState.add({
					message: `Menambahkan ${item.nama_jasa} pada paket`,
					type: 'success'
				});
			}}
		>
			{item.nama_jasa} <span class="font-light"> @ {rupiah(item.harga)}</span>
		</button>
	{/snippet}
</UtilityModal>
