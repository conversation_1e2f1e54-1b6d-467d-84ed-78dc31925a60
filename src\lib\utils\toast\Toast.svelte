<script lang="ts">
	import { fade, slide } from 'svelte/transition';
	import { getToastState } from './ToastState.svelte';

	let toastState = $state(getToastState());
	$effect(() => {
		toastState = getToastState();
	});
</script>

<div class="toast toast-bottom toast-end">
	{#each toastState.toasts as toast (toast.id)}
		<div
			class="alert alert-{toast.type} relative"
			in:slide={{ duration: 300 }}
			out:fade={{ duration: 300 }}
		>
			<button
				class="btn btn-circle btn-ghost btn-xs absolute top-1 right-1"
				type="button"
				onclick={() => toastState.remove(toast)}>✕</button
			>
			<div>
				<span>{toast.message}</span>
			</div>
		</div>
	{/each}
</div>
