<script lang="ts">
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
</script>

<div class="flex content-center gap-4 text-sm">
	<aside class="grow">
		<div class="flex items-center gap-2">
			<div class="w-4/12 shrink-0"><PERSON><PERSON>t</div>
			<div class="w-2">:</div>
			<div class="grow"></div>
		</div>

		<div class="flex items-center gap-2">
			<div class="w-4/12 shrink-0">Kode Sparepart</div>
			<div class="w-2">:</div>
			<div class="grow"></div>
		</div>

		<div class="flex items-center gap-2">
			<div class="w-4/12 shrink-0">Kate<PERSON><PERSON>repart</div>
			<div class="w-2">:</div>
			<div class="grow"></div>
		</div>
	</aside>

	<aside class="grow">
		<div class="flex items-center gap-2">
			<div class="w-4/12 shrink-0">Rentang <PERSON></div>
			<div class="w-2">:</div>
		</div>
		<div class="grid grow grid-cols-2 gap-4">
			<input type="date" class="input input-sm w-full" />
			<input type="date" class="input input-sm w-full" />
		</div>
	</aside>

	<aside class="shrink-0">
		<button class="btn btn-outline btn-primary">
			<Icon icon="mdi:printer" /> Print
		</button>
	</aside>
</div>

<br />

<Table
	table_data={[]}
	table_header={[
		['numbering', 'No.'],
		['custom', 'Tanggal'],
		['nama_sparepart', 'Nama Sparepart'],
		['kode_sparepart', 'Kode Sparepart'],
		['keterangan', 'Keterangan'],
		['masuk', 'Qty Masuk'],
		['keluar', 'Qty Keluar'],
		['stok_barang', 'Stok Barang'],
		['custom', 'Petugas']
	]}
></Table>
