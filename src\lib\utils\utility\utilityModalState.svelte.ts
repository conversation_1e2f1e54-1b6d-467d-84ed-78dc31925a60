import { debounce } from '$lib/scripts/utils';
import { getContext, setContext } from 'svelte';

export interface UtilityModalState {
	modal: HTMLDialogElement | undefined;

	url: string;
	keyword: string;

	fetchUtilityData<T>(): Promise<{ list: T[]; total_rows: number }>;

	list: Record<string, any>[];
	total_rows: number;
	loading: boolean;

	reset(): void;
}

export default class UtilityModalStateClass implements UtilityModalState {
	modal = $state<HTMLDialogElement>();

	url = $state('');
	keyword = $state('');

	list = $state<Record<string, any>[]>([]);
	total_rows = $state<number>(0);
	loading = $state<boolean>(false);

	async fetchUtilityData<T>() {
		const json: {
			data: T[];
			total_rows: number;
		} = await fetch(this.url + `?keyword=${this.keyword}`).then((r) => r.json());

		return { list: json.data, total_rows: json.total_rows };
	}

	reset() {
		this.url = '';
		this.keyword = '';

		this.loading = false;
	}

	constructor() {
		const search = debounce(() => {
			(async () => {
				this.loading = true;

				const json = await this.fetchUtilityData();
				this.list = json.list as Record<string, any>[];
				this.total_rows = json.total_rows;

				this.loading = false;
			})();
		}, 500);

		$effect(() => search(this.url, this.keyword));
	}
}

const UTILITY_MODAL_STATE_KEY = Symbol('utility-modal-state');

export function setUtilityModalState(): void {
	setContext(UTILITY_MODAL_STATE_KEY, new UtilityModalStateClass());
}

export function getUtilityModalState(): UtilityModalState {
	return getContext<UtilityModalState>(UTILITY_MODAL_STATE_KEY);
}
