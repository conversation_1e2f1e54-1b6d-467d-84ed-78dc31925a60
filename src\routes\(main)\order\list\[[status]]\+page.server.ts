import type { Order } from '$lib/schema/general';
import type { OrderStatusType } from '$lib/schema/misc';
import type { TableHeader } from '$lib/table/types';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const orderStatus = params.status as OrderStatusType;
	const orderList = [
		{
			nomor_order: 'AB-123',
			created_at: '2025-01-01',
			customer: {
				nama: '<PERSON>',
				alamat: 'Jl. ABC No. 123',
				no_telp: '081234567890',
				tipe_customer: 'perorangan',
				id: '1'
			},
			status: 'antrian'
		},
		{
			nomor_order: 'AB-124',
			created_at: '2025-01-01',
			customer: {
				nama: '<PERSON>',
				alamat: 'Jl. ABC No. 123',
				no_telp: '081234567890',
				tipe_customer: 'perorangan',
				id: '1'
			},
			status: 'dikerjakan'
		},
		{
			nomor_order: 'AB-125',
			created_at: '2025-01-01',
			customer: {
				nama: '<PERSON>',
				alamat: 'Jl. ABC No. 123',
				no_telp: '081234567890',
				tipe_customer: 'perorangan',
				id: '1'
			},
			status: 'selesai'
		},
		{
			nomor_order: 'AB-127',
			created_at: '2025-01-01',
			customer: {
				nama: 'John Doe',
				alamat: 'Jl. ABC No. 123',
				no_telp: '081234567890',
				tipe_customer: 'perorangan',
				id: '1'
			},
			status: 'void'
		}
	] as Order[];

	const tableHeader: TableHeader<Order> = !orderStatus
		? [
				['numbering', 'No.'],
				['nomor_order', 'Nomor Order'],
				['nomor_invoice', 'Nomor Invoice'],
				['created_at', 'Tanggal Order'],

				['custom', 'Nama Pelanggan'],
				['custom', 'Status'],
				['custom', 'Actions']
			]
		: [
				['numbering', 'No.'],
				['nomor_order', 'Nomor Order'],
				['created_at', 'Tanggal Order'],

				['custom', 'Nama Pelanggan'],
				['custom', 'Status'],
				['custom', 'Actions']
			];

	const filteredOrderList = !params.status
		? orderList
		: orderList.filter((order) =>
				params.status === 'selesai'
					? order.status === 'selesai' || order.status === 'void'
					: params.status === 'proses'
						? order.status === 'dikerjakan'
						: orderStatus === order.status
			);

	return { orderList: filteredOrderList, orderStatus, tableHeader };
};
