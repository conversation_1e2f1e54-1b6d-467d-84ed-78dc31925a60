import type { PageServerLoad } from './$types';

interface Penjualan {
	value: number;
	time: string;
	[key: string | symbol]: number | string | boolean | Date | symbol;
}

export const load: PageServerLoad = async () => {
	const penjualan: Penjualan[] = [
		{ value: 1000000, time: '2025-01-01' },
		{ value: 2000000, time: '2025-01-02' },
		{ value: 3000000, time: '2025-01-03' },
		{ value: 2000000, time: '2025-01-04' },
		{ value: 3500000, time: '2025-01-05' },
		{ value: 6000000, time: '2025-01-06' },
		{ value: 3000000, time: '2025-01-07' },
		{ value: 2000000, time: '2025-01-08' }
	];

	return { penjualan };
};
