import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';

import { retrieve } from '$lib/utils/fetch';
import { Effect } from 'effect';
import { json } from '@sveltejs/kit';

export const GET: RequestHandler = async ({ url, params }) => {
	const keyword = url.searchParams.get('keyword') ?? '';
	const limit = url.searchParams.get('limit') ?? '10';
	const offset = url.searchParams.get('offset') ?? '1';

	const getUtility = retrieve(
		fetch,
		`/${params.endpoint}?keyword=${keyword}&limit=${limit}&offset=${offset}`
	);
	const data = await Effect.runPromise(getUtility);

	return json(data);
};
