<script lang="ts">
	import type { KategoriSparepart } from '$lib/schema/general';
	import Icon from '@iconify/svelte';
	import { slide } from 'svelte/transition';

	interface IProps {
		list: KategoriSparepart[];
	}

	const { list }: IProps = $props();

	let expand = $state(false);
</script>

<div class="mb-4 grid grid-cols-8 gap-3">
	{#each list as kategori (kategori.id_kategori_sparepart)}
		<button class="btn btn-outline btn-sm btn-primary">
			{kategori.nama}
		</button>
	{/each}

	<button class="btn btn-outline btn-sm btn-primary btn-soft" onclick={() => (expand = !expand)}>
		{#if expand}
			Less <Icon icon="mdi:menu-up" font-size="1.3rem" />
		{:else}
			More <Icon icon="mdi:menu-down" font-size="1.3rem" />
		{/if}
	</button>
</div>

<div class="grid grid-cols-8 gap-3" class:hidden={!expand} transition:slide>
	{#each list as kategori (kategori.id_kategori_sparepart)}
		<button class="btn btn-outline btn-sm btn-primary">
			{kategori.nama}
		</button>
	{/each}
</div>
