import { applyAction } from '$app/forms';
import type { ActionResult } from '@sveltejs/kit';
import type { DecodeFormValueErrors } from './validation';
import type { ValidationErrorState } from './validation/ValidationErrorState.svelte';
import { invalidateAll } from '$app/navigation';
import { type ConfirmState } from './confirm/ConfirmState.svelte';

import { twMerge } from 'tailwind-merge';
import { clsx } from 'clsx';
import { type ToastState } from './toast/ToastState.svelte';

export function cn(...inputs: string[]) {
	return twMerge(clsx(inputs));
}

export const arrayToSimpleObject = <T>(array: T[], keyField: keyof T, valueField: keyof T) => {
	return Object.fromEntries(array.map((item) => [item[valueField], item[keyField]]));
};

export const formEnhancementWithValidation = (
	validationErrorState: ValidationErrorState,
	confirmState?: ConfirmState,
	toastState?: ToastState,
	successFeedback?: () => void
) => {
	return async ({ result }: { result: ActionResult }) => {
		await applyAction(result);

		if (result.type === 'failure') {
			console.log(result);
			if (result.data && 'errors' in result.data) {
				validationErrorState.errors = result.data.errors as DecodeFormValueErrors;

				if (toastState) {
					toastState.add({
						message: result.data?.message ?? 'Wrong Form Input',
						type: 'error'
					});
				} else alert('Wrong Form Input');
			}

			if (toastState) {
				toastState.add({
					message: result.data?.message ?? 'Form Action Failed',
					type: 'error',
					durationMs: 8000
				});
			} else alert('Form Action Failed');
		} else if (result.type === 'success') {
			if (toastState) {
				toastState.add({
					message: result.data?.message ?? 'Form Action Success',
					type: 'success'
				});
			} else alert('Form Action Success');

			invalidateAll();
			successFeedback?.();
		}

		if (confirmState) {
			confirmState.loader = '';
			confirmState.modal?.close();
		} else
			console.warn('The Loader will not stop unless you pass the confirmState to this function.');
	};
};

export const formEnhancementBasic = (
	confirmState?: ConfirmState,
	toastState?: ToastState,
	successFeedback?: () => void
) => {
	return async ({ result }: { result: ActionResult }) => {
		await applyAction(result);

		console.log(result);

		if (result.type === 'failure') {
			if (toastState) {
				toastState.add({
					message: result.data?.message ?? 'Form Action Failed',
					type: 'error',
					durationMs: 8000
				});
			} else alert('Form Action Failed');
		} else if (result.type === 'success') {
			const successMessage = result.data?.message ?? 'Form Action Success';
			if (toastState)
				toastState.add({
					message: successMessage,
					type: 'success'
				});
			else alert(successMessage);

			invalidateAll();
			successFeedback?.();
		}

		if (confirmState) {
			confirmState.loader = '';
			confirmState.modal?.close();
		} else
			console.warn('The Loader will not stop unless you pass the confirmState to this function.');
	};
};
