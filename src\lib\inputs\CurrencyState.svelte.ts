export const rupiah = (num: number | undefined) => {
	if (!num) return `Rp 0`;
	return new Intl.NumberFormat('id-ID', {
		style: 'currency',
		currency: 'IDR',
		maximumFractionDigits: 0
	}).format(num);
};

export const numeric = (str: string) => {
	if (!str) return 0;
	if (str.includes(',00')) str = str.replace(',00', '');
	return Number(str.replace(/[^0-9,-]/g, '').replace(/,/g, '.'));
};

function formatNumber(n: string) {
	// format number 1000000 to 1,234,567
	return n.replace(/\D/g, '').replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

export function formatCurrency(input: HTMLInputElement, blur: undefined | 'blur' = undefined) {
	// appends $ to value, validates decimal side
	// and puts cursor back in right position.

	// get input value
	let input_val = input.value;

	// don't validate empty input
	if (input_val === '') return;

	// original length
	const original_len = input_val.length;

	// initial caret position
	let caret_pos = input.selectionStart ?? 0;

	// check for decimal
	if (input_val.indexOf(',') >= 0) {
		// get position of first decimal
		// this prevents multiple decimals from
		// being entered
		const decimal_pos = input_val.indexOf(',');

		// split number by decimal point
		let left_side = input_val.substring(0, decimal_pos);
		let right_side = input_val.substring(decimal_pos);

		// add commas to left side of number
		left_side = formatNumber(left_side);

		// validate right side
		right_side = formatNumber(right_side);

		// On blur make sure 2 numbers after decimal
		if (blur === 'blur') right_side += '';

		// Limit decimal to only 2 digits
		right_side = right_side.substring(0, 2);

		// join number by .
		input_val = 'Rp ' + left_side + ',' + right_side;
	} else {
		// no decimal entered
		// add commas to number
		// remove all non-digits
		input_val = formatNumber(input_val);
		input_val = 'Rp ' + input_val;

		// final formatting
		if (blur === 'blur') input_val += '';
	}

	// send updated string to input
	input.value = input_val;

	// put caret back in the right position
	const updated_len = input_val.length;
	caret_pos = updated_len - original_len + caret_pos;
	input.setSelectionRange(caret_pos, caret_pos);
}
