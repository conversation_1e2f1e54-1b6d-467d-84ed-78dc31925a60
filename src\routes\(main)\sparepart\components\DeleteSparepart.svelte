<script lang="ts">
	import Icon from '@iconify/svelte';

	import { enhance } from '$app/forms';
	import { formEnhancementBasic } from '$lib/utils/index';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	interface IProps {
		id: string;
	}

	const { id }: IProps = $props();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON>n<PERSON>rma<PERSON>',
				description: '<PERSON><PERSON><PERSON>h Anda yakin akan menghapus sparepart ini?',
				loader: 'delete:sparepart:' + id
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	action="?/delete:sparepart"
	method="post"
	use:enhance={() => formEnhancementBasic(confirmState, toastState)}
	class=" mx-auto w-fit border border-gray-100 p-0"
>
	<input type="hidden" name="id" value={id} />

	<button type="button" class="btn btn-sm btn-ghost btn-error" onclick={(e) => confirmation(e)}>
		<ConfirmLoader name="delete:sparepart:{id}">
			<Icon icon="mdi:trash-can" /> Hapus Sparepart
		</ConfirmLoader>
	</button>
</form>
