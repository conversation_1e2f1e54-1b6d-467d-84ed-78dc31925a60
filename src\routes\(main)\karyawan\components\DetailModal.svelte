<script lang="ts">
	import { enhance } from '$app/forms';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';

	import FormField from '$lib/utils/formField/FormField.svelte';
	import { jabatan } from '$lib/schema/literal';

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `<PERSON><PERSON><PERSON><PERSON><PERSON> yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} karyawan ini?`,
				loader: 'action:karyawan'
			},
			() => confirmState.submitForm(e)
		);
	};

	const detailState = getDetailState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();
</script>

<dialog class="modal" bind:this={detailState.modal}>
	<div class="modal-box text-primary" class:bg-base-200={detailState.mode === 'view'}>
		<div class="flex items-center gap-2">
			<figure class="border-dashed p-2">
				<div class="avatar">
					<div class="h-40 w-40 rounded">
						<img
							src="https://img.daisyui.com/images/stock/photo-1534528741775-53994a69daeb.webp"
							alt="Operator Profile Pic"
						/>
					</div>
				</div>
			</figure>
			<div class="grow">
				<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
					<h3 class="text-lg font-bold">Detail Karyawan</h3>
					<form method="dialog">
						<button
							class="btn btn-sm btn-circle btn-soft"
							onclick={() => (validationErrorState.errors = {})}>✕</button
						>
					</form>
				</div>

				<form
					action="?/action:karyawan"
					method="post"
					enctype="multipart/form-data"
					use:enhance={() =>
						formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
							detailState.modal?.close();
						})}
				>
					<FormField
						name="username"
						label="Username"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.username}
					></FormField>

					<br />

					<FormField
						name="password"
						label="Password"
						type="password"
						bind:mode={detailState.mode}
						bind:value={detailState.body.password}
					></FormField>

					<div class="divider"></div>

					{#if detailState.mode !== 'add'}
						<FormField
							name="id_karyawan"
							label="ID Karyawan"
							type="text"
							bind:mode={detailState.mode}
							bind:value={detailState.body.id_karyawan}
							validation={false}
						></FormField>

						<br />
					{/if}

					<FormField
						name="nama"
						label="Nama Karyawan"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.nama}
					></FormField>

					<br />

					{#if detailState.mode !== 'view'}
						<FormField
							name="foto"
							id="foto"
							label="Foto Karyawan"
							type="file"
							bind:mode={detailState.mode}
							bind:value={detailState.body.foto}
							class="file-input"
						></FormField>

						<br />
					{/if}

					<FormField
						name="alamat"
						label="Alamat Karyawan"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.alamat.jalan}
					></FormField>

					<br />

					<FormField
						name="no_telp"
						label="Nomor Telepon"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.no_telp}
					></FormField>

					<br />

					<FormField
						name="jabatan"
						label="Jabatan"
						type="select"
						bind:mode={detailState.mode}
						bind:value={detailState.body.jabatan}
					>
						{#snippet select_options()}
							{#each jabatan as j}
								<option value={j}>{j}</option>
							{/each}
						{/snippet}
					</FormField>

					<br />

					<FormField
						name="keterangan"
						label="Keterangan"
						type="textarea"
						bind:mode={detailState.mode}
						bind:value={detailState.body.keterangan}
					></FormField>

					<div class="divider"></div>

					<input type="hidden" name="karyawan" value={JSON.stringify(detailState.body)} />
					<input type="hidden" name="mode" value={detailState.mode} />

					<button
						type="button"
						class="btn btn-primary w-full"
						onclick={(e) => confirmation(e)}
						class:hidden={detailState.mode === 'view'}
					>
						<ConfirmLoader name="action:karyawan">Simpan</ConfirmLoader>
					</button>
				</form>
			</div>
		</div>
	</div>
</dialog>
