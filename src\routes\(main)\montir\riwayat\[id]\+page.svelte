<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { data } = $props();
</script>

<div class="flex justify-around gap-2">
	<a href="/montir" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button>
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['nomor_order', 'Nomor Order'],
		['created_at', 'Tanggal Order', 'date'],
		['custom', '<PERSON>a <PERSON>'],
		['custom', 'No. Polisi'],
		['custom', 'Jenis Service'],
		['customer', 'Nama Pemilik'],
		['keterangan', 'Keterangan']
	]}
	table_data={data.riwayat}
>
	{#snippet custom({ header, body })}{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={100} />
</div>
