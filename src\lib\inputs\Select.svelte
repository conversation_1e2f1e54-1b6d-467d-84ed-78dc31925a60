<script lang="ts" generics="T extends Record<string, any> | string | number">
	import { cn } from '$lib/utils';
	import type { Snippet } from 'svelte';

	interface Props {
		name: string;
		id: string;

		value: T | { error: string } | null;
		matcher?: keyof T;
		list: T[];

		class?: string;
		children: Snippet;
	}

	let {
		name,
		id,

		list = [],
		value = $bindable(list[0]),
		matcher = undefined,

		class: className,
		children,
		...restProps
	}: Props = $props();

	class SelectState {
		#display = $state<T[keyof T] | string | number>('');

		constructor() {
			if (value === null) value = list[0];

			if (matcher) this.#display = (value as T)[matcher];
			else if (typeof value === 'string' || typeof value === 'number') this.#display = value;
		}

		get display() {
			return this.#display;
		}

		set display(v) {
			this.#display = v;

			if (matcher)
				value = list.find((item) => item[matcher] === v) ?? {
					error: 'Not in given select list.'
				};
			else value = v as T;
		}
	}

	const selectState = $state(new SelectState());
</script>

<input type="hidden" {name} {id} bind:value />

<select
	class={cn('select w-full', className ?? '')}
	{...restProps}
	bind:value={selectState.display}
>
	{@render children()}
</select>
