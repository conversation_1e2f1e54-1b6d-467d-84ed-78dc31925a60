<script lang="ts">
	import { page } from '$app/state';
	import Icon from '@iconify/svelte';

	import logoImage from '$lib/images/logo-image.webp';
	import logoText from '$lib/images/logo-text.webp';

	let { children } = $props();
	let menuOpened = $state(true);
</script>

{#snippet menu(href: string, title: string, icon: string)}
	<a {href}>
		<button
			class="btn flex w-full items-center justify-start gap-4 rounded-none px-4 py-2 text-xs hover:opacity-90
			{page.url.pathname.includes(href)
				? `btn-secondary text-primary font-bold`
				: `btn-primary btn-ghost text-white`}"
		>
			<Icon {icon} font-size="1.2rem" />
			{title}
		</button>
	</a>
{/snippet}

<main class="flex h-screen">
	<aside
		class="bg-primary flex h-screen shrink-0 flex-col overflow-auto sm:w-fit"
		class:hidden={!menuOpened}
	>
		<nav class="flex items-center gap-4 p-4">
			<figure>
				<img src={logoImage} alt="Operator Profile Pic" class="w-10" />
			</figure>
			<div class="items-center text-center">
				<img src={logoText} alt="Operator Profile Pic" class="w-24" />
			</div>
		</nav>

		<div class="my-2 w-full border-b border-white"></div>

		<ul class="menu rounded-box w-56 p-0">
			{@render menu('/order', 'Order', 'ri:list-check-2')}
			{@render menu('/montir', 'Montir', 'material-symbols:engineering')}
			{@render menu('/montir/lokasi', 'Lokasi Montir', 'ri:map-pin-2-fill')}

			<li class="menu-title text-primary-content text-xs">Manajemen</li>

			{@render menu('/karyawan', 'Karyawan', 'mdi:worker')}
			{@render menu('/pelanggan', 'Pelanggan', 'mdi:account-arrow-up')}
			{@render menu('/stok-opname', 'Stok Opname', 'mdi:format-list-numbered-rtl')}

			<li class="menu-title text-primary-content text-xs">Master</li>

			{@render menu('/sparepart', 'Sparepart', 'material-symbols:car-gear')}
			{@render menu('/kendaraan', 'Kendaraan', 'mdi:car')}

			<li>
				<details>
					<summary
						class="btn flex w-full items-center justify-start gap-4 rounded-none px-4 py-2 text-xs hover:opacity-90
			{page.url.pathname === `/jasa`
							? `btn-secondary text-primary font-bold`
							: `btn-primary btn-ghost text-white`}"
					>
						<Icon icon="mdi:account-wrench" font-size="1.2rem" />
						Jasa dan Paket
					</summary>
					<ul>
						{@render menu('/jasa', 'Jasa', 'mdi:account-wrench')}
						{@render menu('/paket', 'Paket', 'mdi:briefcase-search')}
					</ul>
				</details>
			</li>

			<li class="menu-title text-primary-content text-xs">Analytics</li>

			{@render menu('/dashboard', 'Dashboard', 'mdi:view-dashboard')}
			{@render menu('/laporan', 'Laporan', 'mdi:book-account')}
		</ul>
	</aside>

	<section class="flex h-screen grow flex-col">
		<nav class="shadow-base-300 text-primary flex items-center gap-4 px-4 py-2 shadow">
			<button
				class="btn btn-ghost"
				onclick={() => {
					menuOpened = !menuOpened;
				}}
			>
				<Icon icon="majesticons:menu" font-size="1.5rem" />
			</button>

			<h2 class="font-semibold uppercase">{page.url.pathname.replace('/', ' ')}</h2>

			<div class="grow"></div>

			<button
				class="btn btn-ghost flex items-center gap-4"
				popovertarget="profile-popover"
				style="anchor-name: --profile-popover;"
			>
				<figure>
					<div class="avatar">
						<div class="h-8 w-8 rounded-full">
							<img
								src="https://img.daisyui.com/images/stock/photo-*************-53994a69daeb.webp"
								alt="Operator Profile Pic"
							/>
						</div>
					</div>
				</figure>
				<div class="items-center text-center">
					<h2 class="card-title text-sm">NAMA</h2>
				</div>
			</button>

			<div
				class="dropdown bg-base-100 text-primary dropdown-end flex w-64 flex-col items-center justify-center rounded-xl border border-gray-100 p-4 shadow-xl"
				popover
				id="profile-popover"
				style="position-anchor: --profile-popover;"
			>
				<figure>
					<div class="avatar">
						<div class="h-14 w-14 rounded-full">
							<img
								src="https://img.daisyui.com/images/stock/photo-*************-53994a69daeb.webp"
								alt="Operator Profile Pic"
							/>
						</div>
					</div>
				</figure>

				<br />

				<p class="font-light">username_montir</p>
				<h1 class="text-lg font-bold">Nama Montir</h1>
				<p class="font-semibold">role montir</p>

				<br />

				<a href="/logout" data-sveltekit-preload-data="tap" class="text-nowrap">
					<button class="btn btn-wide btn-outline btn-primary">
						<Icon icon="mdi:logout" /> Keluar
					</button>
				</a>
			</div>
		</nav>

		<div class="grow overflow-auto sm:p-2 md:p-4">
			{@render children()}
		</div>
	</section>
</main>
