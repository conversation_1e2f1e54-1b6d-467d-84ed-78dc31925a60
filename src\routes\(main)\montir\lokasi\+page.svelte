<script lang="ts">
	import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'svelte-maplibre-gl';

	const { data } = $props();

	let offset = $state(24);
	let offsets: maplibregl.Offset = $derived({
		top: [0, offset],
		bottom: [0, -offset],
		left: [offset + 12, 0],
		right: [-offset - 12, 0],
		center: [0, 0],
		'top-left': [offset, offset],
		'top-right': [-offset, offset],
		'bottom-left': [offset, -offset],
		'bottom-right': [-offset, -offset]
	});
</script>

<MapLibre
	style="https://basemaps.cartocdn.com/gl/positron-gl-style/style.json"
	class="relative aspect-[9/16] max-h-[70vh] w-full sm:aspect-video sm:max-h-full"
	zoom={11.5}
	center={{ lng: 112.784474, lat: -7.30923 }}
>
	{#each data.montir as montir, i (montir.id_montir)}
		<Marker lnglat={{ lng: montir.lngLat[0], lat: montir.lngLat[1] }}>
			<!-- svelte-ignore a11y_consider_explicit_label -->
			{#snippet content()}
				<button class="btn btn-circle btn-soft border-primary border p-1">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
						<path
							fill="#0040da"
							d="M16.08 2.415A.75.75 0 0 1 16.75 2h1.5a.75.75 0 0 1 .67.415l1 2a.75.75 0 0 1-.019.707L19 6.7V12h1.25a.75.75 0 0 1 .75.75v1.75h-7v-1.75a.75.75 0 0 1 .75-.75H16V6.7l-.901-1.578a.75.75 0 0 1-.02-.707zM14 16v2.5a3.5 3.5 0 1 0 7 0V16zM8.828 2.212a.75.75 0 0 1 .698-.076a5.502 5.502 0 0 1 .51 9.996v7.332a2.536 2.536 0 0 1-5.072 0v-7.332a5.502 5.502 0 0 1 .51-9.996a.75.75 0 0 1 1.026.697V6.5a1 1 0 1 0 2 0V2.833a.75.75 0 0 1 .328-.62"
							stroke-width="0.5"
							stroke="#fff"
						/>
					</svg>
				</button>
			{/snippet}

			<Popup
				class="text-primary w-54 rounded-full shadow-2xl"
				bind:open={data.montir[i].popupOpened}
				offset={offsets}
				closeButton={false}
			>
				<div class="flex flex-col gap-1">
					<div class="flex items-center gap-2">
						<div class="w-3/12 shrink-0 font-semibold text-nowrap">Nama</div>
						<div class="w-4 shrink-0 text-right">:</div>
						<div class="shrink-0 grow font-semibold">{montir.nama}</div>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3/12 shrink-0 font-semibold text-nowrap">ID</div>
						<div class="w-4 text-right">:</div>
						<div class="shrink-0 grow">{montir.id_montir}</div>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3/12 shrink-0 font-semibold text-nowrap">Keahlian</div>
						<div class="w-4 text-right">:</div>
						<div class="shrink-0 grow">{montir.spesialisasi}</div>
					</div>
					<div class="flex items-center gap-2">
						<div class="w-3/12 shrink-0 font-semibold text-nowrap">No. Telp</div>
						<div class="w-4 text-right">:</div>
						<div class="shrink-0 grow">{montir.no_telp}</div>
					</div>
				</div>
			</Popup>
		</Marker>
	{/each}
</MapLibre>
